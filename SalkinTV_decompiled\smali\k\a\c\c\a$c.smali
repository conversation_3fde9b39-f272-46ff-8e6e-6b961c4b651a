.class public interface abstract Lk/a/c/c/a$c;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lk/a/c/c/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "c"
.end annotation


# virtual methods
.method public abstract b(I)Landroid/view/PointerIcon;
.end method

.method public abstract setPointerIcon(Landroid/view/PointerIcon;)V
.end method
