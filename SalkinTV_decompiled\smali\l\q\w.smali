.class public final Ll/q/w;
.super Ll/q/b0;
.source ""


# direct methods
.method public static bridge synthetic a(I)I
    .locals 0

    invoke-static {p0}, Ll/q/y;->a(I)I

    move-result p0

    return p0
.end method

.method public static bridge synthetic g(Ljava/lang/Iterable;)Ljava/util/Map;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<K:",
            "Ljava/lang/Object;",
            "V:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Iterable<",
            "+",
            "Ll/i<",
            "+TK;+TV;>;>;)",
            "Ljava/util/Map<",
            "TK;TV;>;"
        }
    .end annotation

    invoke-static {p0}, Ll/q/z;->g(Ljava/lang/Iterable;)Ljava/util/Map;

    move-result-object p0

    return-object p0
.end method
