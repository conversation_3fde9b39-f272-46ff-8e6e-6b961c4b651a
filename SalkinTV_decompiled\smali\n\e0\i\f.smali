.class public final Ln/e0/i/f;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ln/e0/g/c;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ln/e0/i/f$a;
    }
.end annotation


# static fields
.field private static final f:Lo/f;

.field private static final g:Lo/f;

.field private static final h:Lo/f;

.field private static final i:Lo/f;

.field private static final j:Lo/f;

.field private static final k:Lo/f;

.field private static final l:Lo/f;

.field private static final m:Lo/f;

.field private static final n:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lo/f;",
            ">;"
        }
    .end annotation
.end field

.field private static final o:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lo/f;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field private final a:Ln/t$a;

.field final b:Ln/e0/f/g;

.field private final c:Ln/e0/i/g;

.field private d:Ln/e0/i/i;

.field private final e:Ln/w;


# direct methods
.method static constructor <clinit>()V
    .locals 19

    const-string v0, "connection"

    invoke-static {v0}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v0

    sput-object v0, Ln/e0/i/f;->f:Lo/f;

    const-string v1, "host"

    invoke-static {v1}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v1

    sput-object v1, Ln/e0/i/f;->g:Lo/f;

    const-string v2, "keep-alive"

    invoke-static {v2}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v2

    sput-object v2, Ln/e0/i/f;->h:Lo/f;

    const-string v3, "proxy-connection"

    invoke-static {v3}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v3

    sput-object v3, Ln/e0/i/f;->i:Lo/f;

    const-string v4, "transfer-encoding"

    invoke-static {v4}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v4

    sput-object v4, Ln/e0/i/f;->j:Lo/f;

    const-string v5, "te"

    invoke-static {v5}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v5

    sput-object v5, Ln/e0/i/f;->k:Lo/f;

    const-string v6, "encoding"

    invoke-static {v6}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v6

    sput-object v6, Ln/e0/i/f;->l:Lo/f;

    const-string v7, "upgrade"

    invoke-static {v7}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v7

    sput-object v7, Ln/e0/i/f;->m:Lo/f;

    const/16 v8, 0xc

    new-array v8, v8, [Lo/f;

    const/4 v9, 0x0

    aput-object v0, v8, v9

    const/4 v10, 0x1

    aput-object v1, v8, v10

    const/4 v11, 0x2

    aput-object v2, v8, v11

    const/4 v12, 0x3

    aput-object v3, v8, v12

    const/4 v13, 0x4

    aput-object v5, v8, v13

    const/4 v14, 0x5

    aput-object v4, v8, v14

    const/4 v15, 0x6

    aput-object v6, v8, v15

    const/16 v16, 0x7

    aput-object v7, v8, v16

    sget-object v17, Ln/e0/i/c;->f:Lo/f;

    const/16 v15, 0x8

    aput-object v17, v8, v15

    sget-object v17, Ln/e0/i/c;->g:Lo/f;

    const/16 v18, 0x9

    aput-object v17, v8, v18

    sget-object v17, Ln/e0/i/c;->h:Lo/f;

    const/16 v18, 0xa

    aput-object v17, v8, v18

    sget-object v17, Ln/e0/i/c;->i:Lo/f;

    const/16 v18, 0xb

    aput-object v17, v8, v18

    invoke-static {v8}, Ln/e0/c;->u([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v8

    sput-object v8, Ln/e0/i/f;->n:Ljava/util/List;

    new-array v8, v15, [Lo/f;

    aput-object v0, v8, v9

    aput-object v1, v8, v10

    aput-object v2, v8, v11

    aput-object v3, v8, v12

    aput-object v5, v8, v13

    aput-object v4, v8, v14

    const/4 v0, 0x6

    aput-object v6, v8, v0

    aput-object v7, v8, v16

    invoke-static {v8}, Ln/e0/c;->u([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Ln/e0/i/f;->o:Ljava/util/List;

    return-void
.end method

.method public constructor <init>(Ln/v;Ln/t$a;Ln/e0/f/g;Ln/e0/i/g;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Ln/e0/i/f;->a:Ln/t$a;

    iput-object p3, p0, Ln/e0/i/f;->b:Ln/e0/f/g;

    iput-object p4, p0, Ln/e0/i/f;->c:Ln/e0/i/g;

    invoke-virtual {p1}, Ln/v;->t()Ljava/util/List;

    move-result-object p1

    sget-object p2, Ln/w;->f:Ln/w;

    invoke-interface {p1, p2}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    goto :goto_0

    :cond_0
    sget-object p2, Ln/w;->e:Ln/w;

    :goto_0
    iput-object p2, p0, Ln/e0/i/f;->e:Ln/w;

    return-void
.end method

.method public static g(Ln/y;)Ljava/util/List;
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln/y;",
            ")",
            "Ljava/util/List<",
            "Ln/e0/i/c;",
            ">;"
        }
    .end annotation

    invoke-virtual {p0}, Ln/y;->d()Ln/r;

    move-result-object v0

    new-instance v1, Ljava/util/ArrayList;

    invoke-virtual {v0}, Ln/r;->e()I

    move-result v2

    add-int/lit8 v2, v2, 0x4

    invoke-direct {v1, v2}, Ljava/util/ArrayList;-><init>(I)V

    new-instance v2, Ln/e0/i/c;

    sget-object v3, Ln/e0/i/c;->f:Lo/f;

    invoke-virtual {p0}, Ln/y;->f()Ljava/lang/String;

    move-result-object v4

    invoke-direct {v2, v3, v4}, Ln/e0/i/c;-><init>(Lo/f;Ljava/lang/String;)V

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v2, Ln/e0/i/c;

    sget-object v3, Ln/e0/i/c;->g:Lo/f;

    invoke-virtual {p0}, Ln/y;->h()Ln/s;

    move-result-object v4

    invoke-static {v4}, Ln/e0/g/i;->c(Ln/s;)Ljava/lang/String;

    move-result-object v4

    invoke-direct {v2, v3, v4}, Ln/e0/i/c;-><init>(Lo/f;Ljava/lang/String;)V

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const-string v2, "Host"

    invoke-virtual {p0, v2}, Ln/y;->c(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    if-eqz v2, :cond_0

    new-instance v3, Ln/e0/i/c;

    sget-object v4, Ln/e0/i/c;->i:Lo/f;

    invoke-direct {v3, v4, v2}, Ln/e0/i/c;-><init>(Lo/f;Ljava/lang/String;)V

    invoke-interface {v1, v3}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_0
    new-instance v2, Ln/e0/i/c;

    sget-object v3, Ln/e0/i/c;->h:Lo/f;

    invoke-virtual {p0}, Ln/y;->h()Ln/s;

    move-result-object p0

    invoke-virtual {p0}, Ln/s;->C()Ljava/lang/String;

    move-result-object p0

    invoke-direct {v2, v3, p0}, Ln/e0/i/c;-><init>(Lo/f;Ljava/lang/String;)V

    invoke-interface {v1, v2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    const/4 p0, 0x0

    invoke-virtual {v0}, Ln/r;->e()I

    move-result v2

    :goto_0
    if-ge p0, v2, :cond_2

    invoke-virtual {v0, p0}, Ln/r;->c(I)Ljava/lang/String;

    move-result-object v3

    sget-object v4, Ljava/util/Locale;->US:Ljava/util/Locale;

    invoke-virtual {v3, v4}, Ljava/lang/String;->toLowerCase(Ljava/util/Locale;)Ljava/lang/String;

    move-result-object v3

    invoke-static {v3}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v3

    sget-object v4, Ln/e0/i/f;->n:Ljava/util/List;

    invoke-interface {v4, v3}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v4

    if-nez v4, :cond_1

    new-instance v4, Ln/e0/i/c;

    invoke-virtual {v0, p0}, Ln/r;->f(I)Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v3, v5}, Ln/e0/i/c;-><init>(Lo/f;Ljava/lang/String;)V

    invoke-interface {v1, v4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    :cond_1
    add-int/lit8 p0, p0, 0x1

    goto :goto_0

    :cond_2
    return-object v1
.end method

.method public static h(Ljava/util/List;Ln/w;)Ln/a0$a;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ln/e0/i/c;",
            ">;",
            "Ln/w;",
            ")",
            "Ln/a0$a;"
        }
    .end annotation

    new-instance v0, Ln/r$a;

    invoke-direct {v0}, Ln/r$a;-><init>()V

    invoke-interface {p0}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x0

    const/4 v3, 0x0

    move-object v4, v2

    :goto_0
    if-ge v3, v1, :cond_3

    invoke-interface {p0, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ln/e0/i/c;

    if-nez v5, :cond_0

    if-eqz v4, :cond_2

    iget v5, v4, Ln/e0/g/k;->b:I

    const/16 v6, 0x64

    if-ne v5, v6, :cond_2

    new-instance v0, Ln/r$a;

    invoke-direct {v0}, Ln/r$a;-><init>()V

    move-object v4, v2

    goto :goto_1

    :cond_0
    iget-object v6, v5, Ln/e0/i/c;->a:Lo/f;

    iget-object v5, v5, Ln/e0/i/c;->b:Lo/f;

    invoke-virtual {v5}, Lo/f;->t()Ljava/lang/String;

    move-result-object v5

    sget-object v7, Ln/e0/i/c;->e:Lo/f;

    invoke-virtual {v6, v7}, Lo/f;->equals(Ljava/lang/Object;)Z

    move-result v7

    if-eqz v7, :cond_1

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    const-string v6, "HTTP/1.1 "

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Ln/e0/g/k;->a(Ljava/lang/String;)Ln/e0/g/k;

    move-result-object v4

    goto :goto_1

    :cond_1
    sget-object v7, Ln/e0/i/f;->o:Ljava/util/List;

    invoke-interface {v7, v6}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result v7

    if-nez v7, :cond_2

    sget-object v7, Ln/e0/a;->a:Ln/e0/a;

    invoke-virtual {v6}, Lo/f;->t()Ljava/lang/String;

    move-result-object v6

    invoke-virtual {v7, v0, v6, v5}, Ln/e0/a;->b(Ln/r$a;Ljava/lang/String;Ljava/lang/String;)V

    :cond_2
    :goto_1
    add-int/lit8 v3, v3, 0x1

    goto :goto_0

    :cond_3
    if-eqz v4, :cond_4

    new-instance p0, Ln/a0$a;

    invoke-direct {p0}, Ln/a0$a;-><init>()V

    invoke-virtual {p0, p1}, Ln/a0$a;->m(Ln/w;)Ln/a0$a;

    iget p1, v4, Ln/e0/g/k;->b:I

    invoke-virtual {p0, p1}, Ln/a0$a;->g(I)Ln/a0$a;

    iget-object p1, v4, Ln/e0/g/k;->c:Ljava/lang/String;

    invoke-virtual {p0, p1}, Ln/a0$a;->j(Ljava/lang/String;)Ln/a0$a;

    invoke-virtual {v0}, Ln/r$a;->d()Ln/r;

    move-result-object p1

    invoke-virtual {p0, p1}, Ln/a0$a;->i(Ln/r;)Ln/a0$a;

    return-object p0

    :cond_4
    new-instance p0, Ljava/net/ProtocolException;

    const-string p1, "Expected \':status\' header not present"

    invoke-direct {p0, p1}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    goto :goto_3

    :goto_2
    throw p0

    :goto_3
    goto :goto_2
.end method


# virtual methods
.method public a()V
    .locals 1

    iget-object v0, p0, Ln/e0/i/f;->d:Ln/e0/i/i;

    invoke-virtual {v0}, Ln/e0/i/i;->h()Lo/r;

    move-result-object v0

    invoke-interface {v0}, Lo/r;->close()V

    return-void
.end method

.method public b(Ln/y;)V
    .locals 3

    iget-object v0, p0, Ln/e0/i/f;->d:Ln/e0/i/i;

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p1}, Ln/y;->a()Ln/z;

    move-result-object v0

    if-eqz v0, :cond_1

    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    invoke-static {p1}, Ln/e0/i/f;->g(Ln/y;)Ljava/util/List;

    move-result-object p1

    iget-object v1, p0, Ln/e0/i/f;->c:Ln/e0/i/g;

    invoke-virtual {v1, p1, v0}, Ln/e0/i/g;->D(Ljava/util/List;Z)Ln/e0/i/i;

    move-result-object p1

    iput-object p1, p0, Ln/e0/i/f;->d:Ln/e0/i/i;

    invoke-virtual {p1}, Ln/e0/i/i;->l()Lo/t;

    move-result-object p1

    iget-object v0, p0, Ln/e0/i/f;->a:Ln/t$a;

    invoke-interface {v0}, Ln/t$a;->b()I

    move-result v0

    int-to-long v0, v0

    sget-object v2, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    invoke-virtual {p1, v0, v1, v2}, Lo/t;->g(JLjava/util/concurrent/TimeUnit;)Lo/t;

    iget-object p1, p0, Ln/e0/i/f;->d:Ln/e0/i/i;

    invoke-virtual {p1}, Ln/e0/i/i;->s()Lo/t;

    move-result-object p1

    iget-object v0, p0, Ln/e0/i/f;->a:Ln/t$a;

    invoke-interface {v0}, Ln/t$a;->c()I

    move-result v0

    int-to-long v0, v0

    invoke-virtual {p1, v0, v1, v2}, Lo/t;->g(JLjava/util/concurrent/TimeUnit;)Lo/t;

    return-void
.end method

.method public c(Ln/a0;)Ln/b0;
    .locals 4

    iget-object v0, p0, Ln/e0/i/f;->b:Ln/e0/f/g;

    iget-object v1, v0, Ln/e0/f/g;->f:Ln/p;

    iget-object v0, v0, Ln/e0/f/g;->e:Ln/e;

    invoke-virtual {v1, v0}, Ln/p;->responseBodyStart(Ln/e;)V

    const-string v0, "Content-Type"

    invoke-virtual {p1, v0}, Ln/a0;->m(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-static {p1}, Ln/e0/g/e;->b(Ln/a0;)J

    move-result-wide v1

    new-instance p1, Ln/e0/i/f$a;

    iget-object v3, p0, Ln/e0/i/f;->d:Ln/e0/i/i;

    invoke-virtual {v3}, Ln/e0/i/i;->i()Lo/s;

    move-result-object v3

    invoke-direct {p1, p0, v3}, Ln/e0/i/f$a;-><init>(Ln/e0/i/f;Lo/s;)V

    new-instance v3, Ln/e0/g/h;

    invoke-static {p1}, Lo/l;->b(Lo/s;)Lo/e;

    move-result-object p1

    invoke-direct {v3, v0, v1, v2, p1}, Ln/e0/g/h;-><init>(Ljava/lang/String;JLo/e;)V

    return-object v3
.end method

.method public d()V
    .locals 1

    iget-object v0, p0, Ln/e0/i/f;->c:Ln/e0/i/g;

    invoke-virtual {v0}, Ln/e0/i/g;->flush()V

    return-void
.end method

.method public e(Ln/y;J)Lo/r;
    .locals 0

    iget-object p1, p0, Ln/e0/i/f;->d:Ln/e0/i/i;

    invoke-virtual {p1}, Ln/e0/i/i;->h()Lo/r;

    move-result-object p1

    return-object p1
.end method

.method public f(Z)Ln/a0$a;
    .locals 2

    iget-object v0, p0, Ln/e0/i/f;->d:Ln/e0/i/i;

    invoke-virtual {v0}, Ln/e0/i/i;->q()Ljava/util/List;

    move-result-object v0

    iget-object v1, p0, Ln/e0/i/f;->e:Ln/w;

    invoke-static {v0, v1}, Ln/e0/i/f;->h(Ljava/util/List;Ln/w;)Ln/a0$a;

    move-result-object v0

    if-eqz p1, :cond_0

    sget-object p1, Ln/e0/a;->a:Ln/e0/a;

    invoke-virtual {p1, v0}, Ln/e0/a;->d(Ln/a0$a;)I

    move-result p1

    const/16 v1, 0x64

    if-ne p1, v1, :cond_0

    const/4 p1, 0x0

    return-object p1

    :cond_0
    return-object v0
.end method
