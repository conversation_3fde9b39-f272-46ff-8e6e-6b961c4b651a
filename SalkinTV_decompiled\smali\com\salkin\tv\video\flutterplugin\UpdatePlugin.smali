.class public Lcom/salkin/tv/video/flutterplugin/UpdatePlugin;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lk/a/c/a/j$c;


# static fields
.field public static final CHANNEL:Ljava/lang/String; = "com.salkin.tv.video/update"

.field private static final TAG:Ljava/lang/String; = "MyFlutterPlugin"

.field static channel:Lk/a/c/a/j;


# instance fields
.field private activity:Landroid/app/Activity;


# direct methods
.method private constructor <init>(Landroid/app/Activity;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lcom/salkin/tv/video/flutterplugin/UpdatePlugin;->activity:Landroid/app/Activity;

    return-void
.end method

.method static synthetic access$000(Lcom/salkin/tv/video/flutterplugin/UpdatePlugin;)Landroid/app/Activity;
    .locals 0

    iget-object p0, p0, Lcom/salkin/tv/video/flutterplugin/UpdatePlugin;->activity:Landroid/app/Activity;

    return-object p0
.end method

.method public static registerWith(Lio/flutter/embedding/engine/b;Lio/flutter/embedding/android/g;)V
    .locals 2

    new-instance v0, Lk/a/c/a/j;

    invoke-virtual {p0}, Lio/flutter/embedding/engine/b;->h()Lio/flutter/embedding/engine/f/d;

    move-result-object p0

    const-string v1, "com.salkin.tv.video/update"

    invoke-direct {v0, p0, v1}, Lk/a/c/a/j;-><init>(Lk/a/c/a/c;Ljava/lang/String;)V

    sput-object v0, Lcom/salkin/tv/video/flutterplugin/UpdatePlugin;->channel:Lk/a/c/a/j;

    new-instance p0, Lcom/salkin/tv/video/flutterplugin/UpdatePlugin;

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/flutterplugin/UpdatePlugin;-><init>(Landroid/app/Activity;)V

    sget-object p1, Lcom/salkin/tv/video/flutterplugin/UpdatePlugin;->channel:Lk/a/c/a/j;

    invoke-virtual {p1, p0}, Lk/a/c/a/j;->e(Lk/a/c/a/j$c;)V

    return-void
.end method


# virtual methods
.method public onMethodCall(Lk/a/c/a/i;Lk/a/c/a/j$d;)V
    .locals 2

    iget-object v0, p1, Lk/a/c/a/i;->a:Ljava/lang/String;

    const-string v1, "trun2Second"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const-string p1, "success"

    invoke-interface {p2, p1}, Lk/a/c/a/j$d;->success(Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    iget-object v0, p1, Lk/a/c/a/i;->a:Ljava/lang/String;

    const-string v1, "appDownload"

    invoke-virtual {v0, v1}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    const-string p1, "update_disabled"

    invoke-interface {p2, p1}, Lk/a/c/a/j$d;->success(Ljava/lang/Object;)V

    goto :goto_0

    :cond_1
    invoke-interface {p2}, Lk/a/c/a/j$d;->notImplemented()V

    :goto_0
    return-void
.end method
