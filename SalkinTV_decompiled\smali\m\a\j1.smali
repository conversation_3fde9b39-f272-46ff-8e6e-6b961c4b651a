.class public final Lm/a/j1;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lm/a/j0;


# static fields
.field public static final a:Lm/a/j1;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/j1;

    invoke-direct {v0}, Lm/a/j1;-><init>()V

    sput-object v0, Lm/a/j1;->a:Lm/a/j1;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public g()Ll/s/g;
    .locals 1

    sget-object v0, Ll/s/h;->a:Ll/s/h;

    return-object v0
.end method
