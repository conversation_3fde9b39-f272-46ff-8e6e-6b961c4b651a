.class public abstract Ll/s/j/a/d;
.super Ll/s/j/a/a;
.source ""


# instance fields
.field private final _context:Ll/s/g;

.field private transient intercepted:Ll/s/d;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/s/d<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ll/s/d;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/d<",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    if-eqz p1, :cond_0

    invoke-interface {p1}, Ll/s/d;->getContext()Ll/s/g;

    move-result-object v0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    invoke-direct {p0, p1, v0}, Ll/s/j/a/d;-><init>(Ll/s/d;Ll/s/g;)V

    return-void
.end method

.method public constructor <init>(Ll/s/d;Ll/s/g;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/d<",
            "Ljava/lang/Object;",
            ">;",
            "Ll/s/g;",
            ")V"
        }
    .end annotation

    invoke-direct {p0, p1}, Ll/s/j/a/a;-><init>(Ll/s/d;)V

    iput-object p2, p0, Ll/s/j/a/d;->_context:Ll/s/g;

    return-void
.end method


# virtual methods
.method public getContext()Ll/s/g;
    .locals 1

    iget-object v0, p0, Ll/s/j/a/d;->_context:Ll/s/g;

    invoke-static {v0}, Ll/v/d/j;->b(Ljava/lang/Object;)V

    return-object v0
.end method

.method public final intercepted()Ll/s/d;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ll/s/d<",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Ll/s/j/a/d;->intercepted:Ll/s/d;

    if-nez v0, :cond_2

    invoke-virtual {p0}, Ll/s/j/a/d;->getContext()Ll/s/g;

    move-result-object v0

    sget-object v1, Ll/s/e;->F:Ll/s/e$b;

    invoke-interface {v0, v1}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object v0

    check-cast v0, Ll/s/e;

    if-eqz v0, :cond_0

    invoke-interface {v0, p0}, Ll/s/e;->j(Ll/s/d;)Ll/s/d;

    move-result-object v0

    if-nez v0, :cond_1

    :cond_0
    move-object v0, p0

    :cond_1
    iput-object v0, p0, Ll/s/j/a/d;->intercepted:Ll/s/d;

    :cond_2
    return-object v0
.end method

.method protected releaseIntercepted()V
    .locals 3

    iget-object v0, p0, Ll/s/j/a/d;->intercepted:Ll/s/d;

    if-eqz v0, :cond_0

    if-eq v0, p0, :cond_0

    invoke-virtual {p0}, Ll/s/j/a/d;->getContext()Ll/s/g;

    move-result-object v1

    sget-object v2, Ll/s/e;->F:Ll/s/e$b;

    invoke-interface {v1, v2}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object v1

    invoke-static {v1}, Ll/v/d/j;->b(Ljava/lang/Object;)V

    check-cast v1, Ll/s/e;

    invoke-interface {v1, v0}, Ll/s/e;->d(Ll/s/d;)V

    :cond_0
    sget-object v0, Ll/s/j/a/c;->a:Ll/s/j/a/c;

    iput-object v0, p0, Ll/s/j/a/d;->intercepted:Ll/s/d;

    return-void
.end method
