.class final Ll/a0/n$b;
.super Ll/v/d/k;
.source ""

# interfaces
.implements Ll/v/c/l;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ll/a0/n;->M(Ljava/lang/CharSequence;[Ljava/lang/String;ZI)Ll/z/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ll/v/d/k;",
        "Ll/v/c/l<",
        "Ll/x/c;",
        "Ljava/lang/String;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic a:Ljava/lang/CharSequence;


# direct methods
.method constructor <init>(Ljava/lang/CharSequence;)V
    .locals 0

    iput-object p1, p0, Ll/a0/n$b;->a:Ljava/lang/CharSequence;

    const/4 p1, 0x1

    invoke-direct {p0, p1}, Ll/v/d/k;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final a(Ll/x/c;)Ljava/lang/String;
    .locals 1

    const-string v0, "it"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Ll/a0/n$b;->a:Ljava/lang/CharSequence;

    invoke-static {v0, p1}, Ll/a0/n;->O(Ljava/lang/CharSequence;Ll/x/c;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ll/x/c;

    invoke-virtual {p0, p1}, Ll/a0/n$b;->a(Ll/x/c;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
