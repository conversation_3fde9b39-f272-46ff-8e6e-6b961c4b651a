.class public Ll/v/d/r;
.super Ljava/lang/Object;
.source ""


# static fields
.field private static final a:Ll/v/d/s;

.field private static final b:[Ll/y/c;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    const/4 v0, 0x0

    :try_start_0
    const-string v1, "kotlin.reflect.jvm.internal.ReflectionFactoryImpl"

    invoke-static {v1}, Ljava/lang/Class;->forName(Ljava/lang/String;)Ljava/lang/Class;

    move-result-object v1

    invoke-virtual {v1}, Ljava/lang/Class;->newInstance()Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Ll/v/d/s;
    :try_end_0
    .catch Ljava/lang/ClassCastException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/ClassNotFoundException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/InstantiationException; {:try_start_0 .. :try_end_0} :catch_0
    .catch Ljava/lang/IllegalAccessException; {:try_start_0 .. :try_end_0} :catch_0

    move-object v0, v1

    goto :goto_0

    :catch_0
    nop

    :goto_0
    if-eqz v0, :cond_0

    goto :goto_1

    :cond_0
    new-instance v0, Ll/v/d/s;

    invoke-direct {v0}, Ll/v/d/s;-><init>()V

    :goto_1
    sput-object v0, Ll/v/d/r;->a:Ll/v/d/s;

    const/4 v0, 0x0

    new-array v0, v0, [Ll/y/c;

    sput-object v0, Ll/v/d/r;->b:[Ll/y/c;

    return-void
.end method

.method public static a(Ll/v/d/g;)Ll/y/e;
    .locals 1

    sget-object v0, Ll/v/d/r;->a:Ll/v/d/s;

    invoke-virtual {v0, p0}, Ll/v/d/s;->a(Ll/v/d/g;)Ll/y/e;

    return-object p0
.end method

.method public static b(Ljava/lang/Class;)Ll/y/c;
    .locals 1

    sget-object v0, Ll/v/d/r;->a:Ll/v/d/s;

    invoke-virtual {v0, p0}, Ll/v/d/s;->b(Ljava/lang/Class;)Ll/y/c;

    move-result-object p0

    return-object p0
.end method

.method public static c(Ljava/lang/Class;)Ll/y/d;
    .locals 2

    sget-object v0, Ll/v/d/r;->a:Ll/v/d/s;

    const-string v1, ""

    invoke-virtual {v0, p0, v1}, Ll/v/d/s;->c(Ljava/lang/Class;Ljava/lang/String;)Ll/y/d;

    move-result-object p0

    return-object p0
.end method

.method public static d(Ll/v/d/m;)Ll/y/f;
    .locals 1

    sget-object v0, Ll/v/d/r;->a:Ll/v/d/s;

    invoke-virtual {v0, p0}, Ll/v/d/s;->d(Ll/v/d/m;)Ll/y/f;

    return-object p0
.end method

.method public static e(Ll/v/d/f;)Ljava/lang/String;
    .locals 1

    sget-object v0, Ll/v/d/r;->a:Ll/v/d/s;

    invoke-virtual {v0, p0}, Ll/v/d/s;->e(Ll/v/d/f;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method public static f(Ll/v/d/k;)Ljava/lang/String;
    .locals 1

    sget-object v0, Ll/v/d/r;->a:Ll/v/d/s;

    invoke-virtual {v0, p0}, Ll/v/d/s;->f(Ll/v/d/k;)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method
