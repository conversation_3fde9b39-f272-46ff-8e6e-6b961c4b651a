.class Ln/v$a;
.super Ln/e0/a;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ln/v;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ln/e0/a;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Ln/r$a;Ljava/lang/String;)V
    .locals 0

    invoke-virtual {p1, p2}, Ln/r$a;->b(Ljava/lang/String;)Ln/r$a;

    return-void
.end method

.method public b(Ln/r$a;Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    invoke-virtual {p1, p2, p3}, Ln/r$a;->c(Ljava/lang/String;Ljava/lang/String;)Ln/r$a;

    return-void
.end method

.method public c(Ln/k;Ljavax/net/ssl/SSLSocket;Z)V
    .locals 0

    invoke-virtual {p1, p2, p3}, Ln/k;->a(Ljavax/net/ssl/SSLSocket;Z)V

    return-void
.end method

.method public d(Ln/a0$a;)I
    .locals 0

    iget p1, p1, Ln/a0$a;->c:I

    return p1
.end method

.method public e(Ln/j;Ln/e0/f/c;)Z
    .locals 0

    invoke-virtual {p1, p2}, Ln/j;->b(Ln/e0/f/c;)Z

    move-result p1

    return p1
.end method

.method public f(Ln/j;Ln/a;Ln/e0/f/g;)Ljava/net/Socket;
    .locals 0

    invoke-virtual {p1, p2, p3}, Ln/j;->c(Ln/a;Ln/e0/f/g;)Ljava/net/Socket;

    move-result-object p1

    return-object p1
.end method

.method public g(Ln/a;Ln/a;)Z
    .locals 0

    invoke-virtual {p1, p2}, Ln/a;->d(Ln/a;)Z

    move-result p1

    return p1
.end method

.method public h(Ln/j;Ln/a;Ln/e0/f/g;Ln/c0;)Ln/e0/f/c;
    .locals 0

    invoke-virtual {p1, p2, p3, p4}, Ln/j;->d(Ln/a;Ln/e0/f/g;Ln/c0;)Ln/e0/f/c;

    move-result-object p1

    return-object p1
.end method

.method public i(Ln/j;Ln/e0/f/c;)V
    .locals 0

    invoke-virtual {p1, p2}, Ln/j;->f(Ln/e0/f/c;)V

    return-void
.end method

.method public j(Ln/j;)Ln/e0/f/d;
    .locals 0

    iget-object p1, p1, Ln/j;->e:Ln/e0/f/d;

    return-object p1
.end method
