.class final Lm/a/d;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lm/a/d2;


# static fields
.field public static final a:Lm/a/d;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/d;

    invoke-direct {v0}, Lm/a/d;-><init>()V

    sput-object v0, Lm/a/d;->a:Lm/a/d;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 1

    const-string v0, "Active"

    return-object v0
.end method
