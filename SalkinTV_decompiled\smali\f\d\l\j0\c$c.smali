.class interface abstract Lf/d/l/j0/c$c;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf/d/l/j0/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x60a
    name = "c"
.end annotation


# virtual methods
.method public abstract a()Landroid/content/ClipDescription;
.end method

.method public abstract b()Ljava/lang/Object;
.end method

.method public abstract c()Landroid/net/Uri;
.end method

.method public abstract d()V
.end method

.method public abstract e()Landroid/net/Uri;
.end method
