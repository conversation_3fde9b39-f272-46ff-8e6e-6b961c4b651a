.class public final Ll/s/e$a;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll/s/e;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method public static a(Ll/s/e;Ll/s/g$c;)Ll/s/g$b;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E::",
            "Ll/s/g$b;",
            ">(",
            "Ll/s/e;",
            "Ll/s/g$c<",
            "TE;>;)TE;"
        }
    .end annotation

    const-string v0, "key"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    instance-of v0, p1, Ll/s/b;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    check-cast p1, Ll/s/b;

    invoke-interface {p0}, Ll/s/g$b;->getKey()Ll/s/g$c;

    move-result-object v0

    invoke-virtual {p1, v0}, Ll/s/b;->a(Ll/s/g$c;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1, p0}, Ll/s/b;->b(Ll/s/g$b;)Ll/s/g$b;

    move-result-object p0

    instance-of p1, p0, Ll/s/g$b;

    if-eqz p1, :cond_0

    move-object v1, p0

    :cond_0
    return-object v1

    :cond_1
    sget-object v0, Ll/s/e;->F:Ll/s/e$b;

    if-ne v0, p1, :cond_2

    const-string p1, "null cannot be cast to non-null type E of kotlin.coroutines.ContinuationInterceptor.get"

    invoke-static {p0, p1}, Ll/v/d/j;->c(Ljava/lang/Object;Ljava/lang/String;)V

    goto :goto_0

    :cond_2
    move-object p0, v1

    :goto_0
    return-object p0
.end method

.method public static b(Ll/s/e;Ll/s/g$c;)Ll/s/g;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/e;",
            "Ll/s/g$c<",
            "*>;)",
            "Ll/s/g;"
        }
    .end annotation

    const-string v0, "key"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    instance-of v0, p1, Ll/s/b;

    if-eqz v0, :cond_1

    check-cast p1, Ll/s/b;

    invoke-interface {p0}, Ll/s/g$b;->getKey()Ll/s/g$c;

    move-result-object v0

    invoke-virtual {p1, v0}, Ll/s/b;->a(Ll/s/g$c;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p1, p0}, Ll/s/b;->b(Ll/s/g$b;)Ll/s/g$b;

    move-result-object p1

    if-eqz p1, :cond_0

    sget-object p0, Ll/s/h;->a:Ll/s/h;

    :cond_0
    return-object p0

    :cond_1
    sget-object v0, Ll/s/e;->F:Ll/s/e$b;

    if-ne v0, p1, :cond_2

    sget-object p0, Ll/s/h;->a:Ll/s/h;

    :cond_2
    return-object p0
.end method
