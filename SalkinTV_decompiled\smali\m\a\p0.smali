.class public final Lm/a/p0;
.super Lm/a/b1;
.source ""

# interfaces
.implements Ljava/lang/Runnable;


# static fields
.field private static volatile _thread:Ljava/lang/Thread;

.field private static volatile debugStatus:I

.field public static final g:Lm/a/p0;

.field private static final h:J


# direct methods
.method static constructor <clinit>()V
    .locals 4

    new-instance v0, Lm/a/p0;

    invoke-direct {v0}, Lm/a/p0;-><init>()V

    sput-object v0, Lm/a/p0;->g:Lm/a/p0;

    const/4 v1, 0x0

    const/4 v2, 0x1

    const/4 v3, 0x0

    invoke-static {v0, v1, v2, v3}, Lm/a/a1;->a0(Lm/a/a1;ZILjava/lang/Object;)V

    sget-object v0, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    const-wide/16 v1, 0x3e8

    :try_start_0
    const-string v3, "kotlinx.coroutines.DefaultExecutor.keepAlive"

    invoke-static {v3, v1, v2}, Ljava/lang/Long;->getLong(Ljava/lang/String;J)Ljava/lang/Long;

    move-result-object v1
    :try_end_0
    .catch Ljava/lang/SecurityException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    invoke-static {v1, v2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    :goto_0
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    move-result-wide v1

    invoke-virtual {v0, v1, v2}, Ljava/util/concurrent/TimeUnit;->toNanos(J)J

    move-result-wide v0

    sput-wide v0, Lm/a/p0;->h:J

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lm/a/b1;-><init>()V

    return-void
.end method

.method private final declared-synchronized A0()Z
    .locals 1

    monitor-enter p0

    :try_start_0
    invoke-direct {p0}, Lm/a/p0;->z0()Z

    move-result v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v0, :cond_0

    const/4 v0, 0x0

    monitor-exit p0

    return v0

    :cond_0
    const/4 v0, 0x1

    :try_start_1
    sput v0, Lm/a/p0;->debugStatus:I

    invoke-virtual {p0}, Ljava/lang/Object;->notifyAll()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method private final B0()V
    .locals 2

    new-instance v0, Ljava/util/concurrent/RejectedExecutionException;

    const-string v1, "DefaultExecutor was shut down. This error indicates that Dispatchers.shutdown() was invoked prior to completion of exiting coroutines, leaving coroutines in incomplete state. Please refer to Dispatchers.shutdown documentation for more details"

    invoke-direct {v0, v1}, Ljava/util/concurrent/RejectedExecutionException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private final declared-synchronized w0()V
    .locals 1

    monitor-enter p0

    :try_start_0
    invoke-direct {p0}, Lm/a/p0;->z0()Z

    move-result v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v0, :cond_0

    monitor-exit p0

    return-void

    :cond_0
    const/4 v0, 0x3

    :try_start_1
    sput v0, Lm/a/p0;->debugStatus:I

    invoke-virtual {p0}, Lm/a/b1;->r0()V

    invoke-virtual {p0}, Ljava/lang/Object;->notifyAll()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    monitor-exit p0

    return-void

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method private final declared-synchronized x0()Ljava/lang/Thread;
    .locals 2

    monitor-enter p0

    :try_start_0
    sget-object v0, Lm/a/p0;->_thread:Ljava/lang/Thread;

    if-nez v0, :cond_0

    new-instance v0, Ljava/lang/Thread;

    const-string v1, "kotlinx.coroutines.DefaultExecutor"

    invoke-direct {v0, p0, v1}, Ljava/lang/Thread;-><init>(Ljava/lang/Runnable;Ljava/lang/String;)V

    sput-object v0, Lm/a/p0;->_thread:Ljava/lang/Thread;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Ljava/lang/Thread;->setDaemon(Z)V

    invoke-virtual {v0}, Ljava/lang/Thread;->start()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_0
    monitor-exit p0

    return-object v0

    :catchall_0
    move-exception v0

    monitor-exit p0

    throw v0
.end method

.method private final y0()Z
    .locals 2

    sget v0, Lm/a/p0;->debugStatus:I

    const/4 v1, 0x4

    if-ne v0, v1, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method private final z0()Z
    .locals 2

    sget v0, Lm/a/p0;->debugStatus:I

    const/4 v1, 0x2

    if-eq v0, v1, :cond_1

    const/4 v1, 0x3

    if-ne v0, v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 v0, 0x1

    :goto_1
    return v0
.end method


# virtual methods
.method public e0()V
    .locals 1

    const/4 v0, 0x4

    sput v0, Lm/a/p0;->debugStatus:I

    invoke-super {p0}, Lm/a/b1;->e0()V

    return-void
.end method

.method protected f0()Ljava/lang/Thread;
    .locals 1

    sget-object v0, Lm/a/p0;->_thread:Ljava/lang/Thread;

    if-nez v0, :cond_0

    invoke-direct {p0}, Lm/a/p0;->x0()Ljava/lang/Thread;

    move-result-object v0

    :cond_0
    return-object v0
.end method

.method protected g0(JLm/a/b1$a;)V
    .locals 0

    invoke-direct {p0}, Lm/a/p0;->B0()V

    const/4 p1, 0x0

    throw p1
.end method

.method public l0(Ljava/lang/Runnable;)V
    .locals 1

    invoke-direct {p0}, Lm/a/p0;->y0()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-super {p0, p1}, Lm/a/b1;->l0(Ljava/lang/Runnable;)V

    return-void

    :cond_0
    invoke-direct {p0}, Lm/a/p0;->B0()V

    const/4 p1, 0x0

    throw p1
.end method

.method public run()V
    .locals 12

    sget-object v0, Lm/a/i2;->a:Lm/a/i2;

    invoke-virtual {v0, p0}, Lm/a/i2;->c(Lm/a/a1;)V

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lm/a/b;->c()V

    :goto_0
    const/4 v0, 0x0

    :try_start_0
    invoke-direct {p0}, Lm/a/p0;->A0()Z

    move-result v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v1, :cond_3

    sput-object v0, Lm/a/p0;->_thread:Ljava/lang/Thread;

    invoke-direct {p0}, Lm/a/p0;->w0()V

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_1

    :cond_1
    invoke-virtual {v0}, Lm/a/b;->g()V

    :goto_1
    invoke-virtual {p0}, Lm/a/b1;->o0()Z

    move-result v0

    if-nez v0, :cond_2

    invoke-virtual {p0}, Lm/a/p0;->f0()Ljava/lang/Thread;

    :cond_2
    return-void

    :cond_3
    const-wide v1, 0x7fffffffffffffffL

    move-wide v3, v1

    :cond_4
    :goto_2
    :try_start_1
    invoke-static {}, Ljava/lang/Thread;->interrupted()Z

    invoke-virtual {p0}, Lm/a/b1;->p0()J

    move-result-wide v5

    const-wide/16 v7, 0x0

    cmp-long v9, v5, v1

    if-nez v9, :cond_b

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v9

    if-nez v9, :cond_5

    move-object v9, v0

    goto :goto_3

    :cond_5
    invoke-virtual {v9}, Lm/a/b;->a()J

    move-result-wide v9

    invoke-static {v9, v10}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v9

    :goto_3
    if-nez v9, :cond_6

    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v9

    goto :goto_4

    :cond_6
    invoke-virtual {v9}, Ljava/lang/Long;->longValue()J

    move-result-wide v9

    :goto_4
    cmp-long v11, v3, v1

    if-nez v11, :cond_7

    sget-wide v3, Lm/a/p0;->h:J
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    add-long/2addr v3, v9

    :cond_7
    sub-long v9, v3, v9

    cmp-long v11, v9, v7

    if-gtz v11, :cond_a

    sput-object v0, Lm/a/p0;->_thread:Ljava/lang/Thread;

    invoke-direct {p0}, Lm/a/p0;->w0()V

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v0

    if-nez v0, :cond_8

    goto :goto_5

    :cond_8
    invoke-virtual {v0}, Lm/a/b;->g()V

    :goto_5
    invoke-virtual {p0}, Lm/a/b1;->o0()Z

    move-result v0

    if-nez v0, :cond_9

    invoke-virtual {p0}, Lm/a/p0;->f0()Ljava/lang/Thread;

    :cond_9
    return-void

    :cond_a
    :try_start_2
    invoke-static {v5, v6, v9, v10}, Ll/x/d;->d(JJ)J

    move-result-wide v5

    goto :goto_6

    :cond_b
    move-wide v3, v1

    :goto_6
    cmp-long v9, v5, v7

    if-lez v9, :cond_4

    invoke-direct {p0}, Lm/a/p0;->z0()Z

    move-result v7
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    if-eqz v7, :cond_e

    sput-object v0, Lm/a/p0;->_thread:Ljava/lang/Thread;

    invoke-direct {p0}, Lm/a/p0;->w0()V

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v0

    if-nez v0, :cond_c

    goto :goto_7

    :cond_c
    invoke-virtual {v0}, Lm/a/b;->g()V

    :goto_7
    invoke-virtual {p0}, Lm/a/b1;->o0()Z

    move-result v0

    if-nez v0, :cond_d

    invoke-virtual {p0}, Lm/a/p0;->f0()Ljava/lang/Thread;

    :cond_d
    return-void

    :cond_e
    :try_start_3
    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v7

    if-nez v7, :cond_f

    move-object v7, v0

    goto :goto_8

    :cond_f
    invoke-virtual {v7, p0, v5, v6}, Lm/a/b;->b(Ljava/lang/Object;J)V

    sget-object v7, Ll/p;->a:Ll/p;

    :goto_8
    if-nez v7, :cond_4

    invoke-static {p0, v5, v6}, Ljava/util/concurrent/locks/LockSupport;->parkNanos(Ljava/lang/Object;J)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    goto/16 :goto_2

    :catchall_0
    move-exception v1

    sput-object v0, Lm/a/p0;->_thread:Ljava/lang/Thread;

    invoke-direct {p0}, Lm/a/p0;->w0()V

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v0

    if-nez v0, :cond_10

    goto :goto_9

    :cond_10
    invoke-virtual {v0}, Lm/a/b;->g()V

    :goto_9
    invoke-virtual {p0}, Lm/a/b1;->o0()Z

    move-result v0

    if-nez v0, :cond_11

    invoke-virtual {p0}, Lm/a/p0;->f0()Ljava/lang/Thread;

    :cond_11
    goto :goto_b

    :goto_a
    throw v1

    :goto_b
    goto :goto_a
.end method
