.class public interface abstract Lo/e;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lo/s;
.implements Ljava/nio/channels/ReadableByteChannel;


# virtual methods
.method public abstract A()S
.end method

.method public abstract F(J)Ljava/lang/String;
.end method

.method public abstract G()S
.end method

.method public abstract I(J)V
.end method

.method public abstract L(B)J
.end method

.method public abstract M(JLo/f;)Z
.end method

.method public abstract N()J
.end method

.method public abstract O(Ljava/nio/charset/Charset;)Ljava/lang/String;
.end method

.method public abstract P()Ljava/io/InputStream;
.end method

.method public abstract R()B
.end method

.method public abstract c([B)V
.end method

.method public abstract e()Lo/c;
.end method

.method public abstract i(J)Lo/f;
.end method

.method public abstract k(J)V
.end method

.method public abstract n()I
.end method

.method public abstract q()Ljava/lang/String;
.end method

.method public abstract s()[B
.end method

.method public abstract t()I
.end method

.method public abstract u()Z
.end method

.method public abstract w(J)[B
.end method
