.class final Lm/a/p2/e;
.super Lm/a/p2/h;
.source ""


# annotations
.annotation build Lorg/codehaus/mojo/animal_sniffer/IgnoreJRERequirement;
.end annotation


# static fields
.field public static final a:Lm/a/p2/e;

.field private static final b:Lm/a/p2/e$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/p2/e;

    invoke-direct {v0}, Lm/a/p2/e;-><init>()V

    sput-object v0, Lm/a/p2/e;->a:Lm/a/p2/e;

    new-instance v0, Lm/a/p2/e$a;

    invoke-direct {v0}, Lm/a/p2/e$a;-><init>()V

    sput-object v0, Lm/a/p2/e;->b:Lm/a/p2/e$a;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lm/a/p2/h;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Ljava/lang/Class;)Ll/v/c/l;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "+",
            "Ljava/lang/Throwable;",
            ">;)",
            "Ll/v/c/l<",
            "Ljava/lang/Throwable;",
            "Ljava/lang/Throwable;",
            ">;"
        }
    .end annotation

    sget-object v0, Lm/a/p2/e;->b:Lm/a/p2/e$a;

    invoke-virtual {v0, p1}, Lm/a/p2/e$a;->get(Ljava/lang/Class;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ll/v/c/l;

    return-object p1
.end method
