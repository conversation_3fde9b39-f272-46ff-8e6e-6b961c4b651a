.class public interface abstract Lk/a/c/a/k;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract a(Ljava/lang/Object;)Ljava/nio/ByteBuffer;
.end method

.method public abstract b(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/String;)Ljava/nio/ByteBuffer;
.end method

.method public abstract c(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;)Ljava/nio/ByteBuffer;
.end method

.method public abstract d(Ljava/nio/ByteBuffer;)Lk/a/c/a/i;
.end method

.method public abstract e(Ljava/nio/ByteBuffer;)Ljava/lang/Object;
.end method

.method public abstract f(Lk/a/c/a/i;)Ljava/nio/ByteBuffer;
.end method
