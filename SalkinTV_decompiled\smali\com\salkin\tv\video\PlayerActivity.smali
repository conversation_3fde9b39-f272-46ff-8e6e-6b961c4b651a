.class public Lcom/salkin/tv/video/PlayerActivity;
.super Landroidx/fragment/app/d;
.source ""

# interfaces
.implements Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;
.implements Lcom/salkin/tv/video/views/PlayerRightView$PlayerRightViewInterface;
.implements Lcom/salkin/tv/video/views/PlayerVipPurchase$PlayerVipPurchaseInteracface;


# instance fields
.field mHandler:Landroid/os/Handler;
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "HandlerLeak"
        }
    .end annotation
.end field

.field mIjkVideoView:Lcom/salkin/tv/video/widgets/IjkVideoView;

.field mUIHandler:Landroid/os/Handler;

.field private movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

.field player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

.field playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

.field playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

.field playerVipPurchase:Lcom/salkin/tv/video/views/PlayerVipPurchase;

.field runnableDuration:Ljava/lang/Runnable;

.field runnableSendStatic:Ljava/lang/Runnable;

.field timerTask:Ljava/util/TimerTask;

.field updateProgressTimer:Ljava/util/Timer;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Landroidx/fragment/app/d;-><init>()V

    new-instance v0, Landroid/os/Handler;

    invoke-direct {v0}, Landroid/os/Handler;-><init>()V

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mUIHandler:Landroid/os/Handler;

    new-instance v0, Lcom/salkin/tv/video/PlayerActivity$1;

    invoke-direct {v0, p0}, Lcom/salkin/tv/video/PlayerActivity$1;-><init>(Lcom/salkin/tv/video/PlayerActivity;)V

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mHandler:Landroid/os/Handler;

    new-instance v0, Lcom/salkin/tv/video/PlayerActivity$2;

    invoke-direct {v0, p0}, Lcom/salkin/tv/video/PlayerActivity$2;-><init>(Lcom/salkin/tv/video/PlayerActivity;)V

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->runnableDuration:Ljava/lang/Runnable;

    new-instance v0, Lcom/salkin/tv/video/PlayerActivity$3;

    invoke-direct {v0, p0}, Lcom/salkin/tv/video/PlayerActivity$3;-><init>(Lcom/salkin/tv/video/PlayerActivity;)V

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->runnableSendStatic:Ljava/lang/Runnable;

    new-instance v0, Lcom/salkin/tv/video/PlayerActivity$4;

    invoke-direct {v0, p0}, Lcom/salkin/tv/video/PlayerActivity$4;-><init>(Lcom/salkin/tv/video/PlayerActivity;)V

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->timerTask:Ljava/util/TimerTask;

    return-void
.end method

.method static synthetic access$000(Lcom/salkin/tv/video/PlayerActivity;)Lcom/salkin/tv/video/entity/MovieDataEntity;
    .locals 0

    iget-object p0, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    return-object p0
.end method

.method private clearUIHandler()V
    .locals 2

    :try_start_0
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mUIHandler:Landroid/os/Handler;

    iget-object v1, p0, Lcom/salkin/tv/video/PlayerActivity;->runnableDuration:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    :goto_0
    :try_start_1
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mUIHandler:Landroid/os/Handler;

    iget-object v1, p0, Lcom/salkin/tv/video/PlayerActivity;->runnableSendStatic:Ljava/lang/Runnable;

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeCallbacks(Ljava/lang/Runnable;)V
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_1

    goto :goto_1

    :catch_1
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    :goto_1
    return-void
.end method

.method private synthetic e(Landroid/view/View;)V
    .locals 0

    invoke-direct {p0}, Lcom/salkin/tv/video/PlayerActivity;->switchPlayerState()V

    return-void
.end method

.method private synthetic g(Landroid/view/View;)V
    .locals 0

    invoke-direct {p0}, Lcom/salkin/tv/video/PlayerActivity;->switchPlayerState()V

    return-void
.end method

.method private synthetic i(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;Ljava/lang/String;)V
    .locals 3

    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "basbas bas tv sign:"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    if-eqz p2, :cond_0

    const-string v0, "error sign"

    invoke-virtual {p2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    iget-object p1, p1, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;->url:Ljava/lang/String;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-interface {v0, p1}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->startPlay(Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    iget-object p2, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    iget-object p1, p1, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;->url:Ljava/lang/String;

    invoke-interface {p2, p1}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->startPlay(Ljava/lang/String;)V

    :goto_0
    invoke-direct {p0}, Lcom/salkin/tv/video/PlayerActivity;->clearUIHandler()V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->mUIHandler:Landroid/os/Handler;

    iget-object p2, p0, Lcom/salkin/tv/video/PlayerActivity;->runnableDuration:Ljava/lang/Runnable;

    const-wide/16 v0, 0x7530

    invoke-virtual {p1, p2, v0, v1}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    return-void
.end method

.method private initIjkPlayer()V
    .locals 3

    :try_start_0
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mIjkVideoView:Lcom/salkin/tv/video/widgets/IjkVideoView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/widget/FrameLayout;->setVisibility(I)V

    const-string v0, "ijk"

    invoke-static {v0, p0}, Lcom/salkin/tv/video/factory/PlayerFactory;->createPlayer(Ljava/lang/String;Landroid/content/Context;)Lcom/salkin/tv/video/interfaces/PlayerInterface;

    move-result-object v0

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    iget-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->mIjkVideoView:Lcom/salkin/tv/video/widgets/IjkVideoView;

    invoke-interface {v0, v2}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->setView(Landroid/view/View;)V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->initPlayer()V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0, v1}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->enableHardWareDecode(Z)V

    new-instance v0, Lcom/salkin/tv/video/widgets/Settings;

    invoke-direct {v0, p0}, Lcom/salkin/tv/video/widgets/Settings;-><init>(Landroid/content/Context;)V

    invoke-virtual {v0}, Lcom/salkin/tv/video/widgets/Settings;->getScreenSizeType()I

    move-result v0

    iget-object v1, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v1, v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->setScreenSizeType(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "\u062e\u0627\u062a\u0627\u0644\u0649\u0642 \u0643\u06c6\u0631\u06c8\u0644\u062f\u0649 -2"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-static {p0, v0}, Lcom/salkin/tv/video/tools/MyToast;->showError(Landroid/content/Context;Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method private synthetic k(JI)V
    .locals 1

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    long-to-int p2, p1

    div-int/lit16 p2, p2, 0x3e8

    invoke-interface {v0, p2}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->setStartTime(I)V

    invoke-direct {p0, p3}, Lcom/salkin/tv/video/PlayerActivity;->updatePlayer3ButtonState(I)V

    return-void
.end method

.method private synthetic m()V
    .locals 2

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    const/16 v1, 0x8

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerVipPurchase:Lcom/salkin/tv/video/views/PlayerVipPurchase;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->stop()V

    return-void
.end method

.method private playByIndexString(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;)V
    .locals 3

    invoke-static {}, Lcom/salkin/tv/video/MyApp;->getInstance()Lcom/salkin/tv/video/MyApp;

    move-result-object v0

    invoke-virtual {v0, p1}, Lcom/salkin/tv/video/MyApp;->setCurrentPlayingItem(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;)V

    :try_start_0
    iget v0, p1, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;->item_player:I
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    const/4 v1, 0x1

    const-string v2, "basbas bas tv sign: player type 1"

    if-ne v0, v1, :cond_0

    :try_start_1
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {v0, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    new-instance v0, Lcom/salkin/tv/video/tools/MySign;

    invoke-direct {v0}, Lcom/salkin/tv/video/tools/MySign;-><init>()V

    new-instance v1, Lcom/salkin/tv/video/d;

    invoke-direct {v1, p0, p1}, Lcom/salkin/tv/video/d;-><init>(Lcom/salkin/tv/video/PlayerActivity;Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;)V

    invoke-virtual {v0, v1}, Lcom/salkin/tv/video/tools/MySign;->getSign(Lcom/salkin/tv/video/tools/MySign$MySignInterfaces;)V

    goto :goto_0

    :cond_0
    sget-object v0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    invoke-virtual {v0, v2}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    iget-object p1, p1, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;->url:Ljava/lang/String;

    invoke-interface {v0, p1}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->startPlay(Ljava/lang/String;)V

    invoke-direct {p0}, Lcom/salkin/tv/video/PlayerActivity;->clearUIHandler()V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->mUIHandler:Landroid/os/Handler;

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->runnableDuration:Ljava/lang/Runnable;

    const-wide/16 v1, 0x7530

    invoke-virtual {p1, v0, v1, v2}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z
    :try_end_1
    .catch Ljava/lang/Exception; {:try_start_1 .. :try_end_1} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Exception;->printStackTrace()V

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "\u062e\u0627\u062a\u0627\u0644\u0649\u0642 \u0643\u06c6\u0631\u06c8\u0644\u062f\u0649 -4"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-static {p0, p1}, Lcom/salkin/tv/video/tools/MyToast;->showError(Landroid/content/Context;Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method private playByType(I)V
    .locals 6

    :try_start_0
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->getCurrentPosition()J

    move-result-wide v0

    iget-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v2}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->stop()V

    const-string v2, "ijk"

    invoke-static {v2, p0}, Lcom/salkin/tv/video/factory/PlayerFactory;->createPlayer(Ljava/lang/String;Landroid/content/Context;)Lcom/salkin/tv/video/interfaces/PlayerInterface;

    move-result-object v2

    iput-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    const v2, 0x7f0f006b

    invoke-virtual {p0, v2}, Landroid/app/Activity;->getString(I)Ljava/lang/String;

    move-result-object v2

    invoke-static {p0}, Landroid/preference/PreferenceManager;->getDefaultSharedPreferences(Landroid/content/Context;)Landroid/content/SharedPreferences;

    move-result-object v3

    invoke-interface {v3}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;

    move-result-object v3

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-interface {v3, v2, v4}, Landroid/content/SharedPreferences$Editor;->putString(Ljava/lang/String;Ljava/lang/String;)Landroid/content/SharedPreferences$Editor;

    move-result-object v2

    invoke-interface {v2}, Landroid/content/SharedPreferences$Editor;->apply()V

    iget-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->mIjkVideoView:Lcom/salkin/tv/video/widgets/IjkVideoView;

    invoke-virtual {v2, p0}, Lcom/salkin/tv/video/widgets/IjkVideoView;->initVideoView(Landroid/content/Context;)V

    iget-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    iget-object v3, p0, Lcom/salkin/tv/video/PlayerActivity;->mIjkVideoView:Lcom/salkin/tv/video/widgets/IjkVideoView;

    invoke-interface {v2, v3}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->setView(Landroid/view/View;)V

    iget-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v2}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->initPlayer()V

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/PlayerActivity;->updatePlayer3ButtonState(I)V

    new-instance v2, Landroid/os/Handler;

    invoke-direct {v2}, Landroid/os/Handler;-><init>()V

    new-instance v3, Lcom/salkin/tv/video/c;

    invoke-direct {v3, p0, v0, v1, p1}, Lcom/salkin/tv/video/c;-><init>(Lcom/salkin/tv/video/PlayerActivity;JI)V

    const-wide/16 v0, 0x3e8

    invoke-virtual {v2, v3, v0, v1}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Exception;->printStackTrace()V

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "-----"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p1}, Ljava/lang/Exception;->getMessage()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v0, "basbas"

    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    :goto_0
    return-void
.end method

.method private playItem()V
    .locals 6

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object v0

    const-string v1, "index"

    invoke-virtual {v0, v1}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    iget-object v1, v1, Lcom/salkin/tv/video/entity/MovieDataEntity;->child_list:Ljava/util/List;

    invoke-interface {v1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v1

    const/4 v2, 0x0

    :cond_0
    :goto_0
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_1

    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    iget v5, v3, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;->index:I

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v5, ""

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v0, v4}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v4

    if-eqz v4, :cond_0

    invoke-direct {p0, v3}, Lcom/salkin/tv/video/PlayerActivity;->playByIndexString(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;)V

    const/4 v2, 0x1

    goto :goto_0

    :cond_1
    if-nez v2, :cond_2

    const-string v0, "\u062e\u0627\u062a\u0627\u0644\u0649\u0642 \u0643\u06c6\u0631\u06c8\u0644\u062f\u0649 -3"

    invoke-static {p0, v0}, Lcom/salkin/tv/video/tools/MyToast;->showError(Landroid/content/Context;Ljava/lang/String;)V

    :cond_2
    return-void
.end method

.method public static start(Landroid/content/Context;Ljava/lang/Object;)V
    .locals 3

    new-instance v0, Lg/c/a/a;

    invoke-direct {v0}, Lg/c/a/a;-><init>()V

    invoke-static {v0}, Lg/c/a/e;->a(Lg/c/a/b;)V

    instance-of v0, p1, Ljava/util/HashMap;

    if-eqz v0, :cond_0

    new-instance v0, Landroid/content/Intent;

    const-class v1, Lcom/salkin/tv/video/PlayerActivity;

    invoke-direct {v0, p0, v1}, Landroid/content/Intent;-><init>(Landroid/content/Context;Ljava/lang/Class;)V

    check-cast p1, Ljava/util/HashMap;

    const-string v1, "token"

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    const-string v1, "index"

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    const-string v1, "last_pos"

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ljava/lang/String;

    invoke-virtual {v0, v1, v2}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    const-string v1, "movie_data"

    invoke-virtual {p1, v1}, Ljava/util/HashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    invoke-virtual {v0, v1, p1}, Landroid/content/Intent;->putExtra(Ljava/lang/String;Ljava/lang/String;)Landroid/content/Intent;

    invoke-virtual {p0, v0}, Landroid/content/Context;->startActivity(Landroid/content/Intent;)V

    :cond_0
    return-void
.end method

.method private switchPlayerState()V
    .locals 2

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isVip()Z

    move-result v0

    if-nez v0, :cond_2

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isRightView()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_1

    :cond_0
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->isPlaying()Z

    move-result v0

    if-eqz v0, :cond_1

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->pause()V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->resume()V

    :goto_0
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->startHideHandler()V

    :cond_2
    :goto_1
    return-void
.end method

.method private updatePlayer3ButtonState(I)V
    .locals 5

    invoke-static {}, Lcom/salkin/tv/video/MyApp;->getInstance()Lcom/salkin/tv/video/MyApp;

    move-result-object v0

    invoke-virtual {v0}, Lcom/salkin/tv/video/MyApp;->getPlayingItem()Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;

    move-result-object v0

    invoke-direct {p0, v0}, Lcom/salkin/tv/video/PlayerActivity;->playByIndexString(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;)V

    const v0, 0x7f090088

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v1, v2}, Landroid/view/View;->setSelected(Z)V

    const v1, 0x7f090089

    invoke-virtual {p0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v3

    invoke-virtual {v3, v2}, Landroid/view/View;->setSelected(Z)V

    const v3, 0x7f09008a

    invoke-virtual {p0, v3}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v4

    invoke-virtual {v4, v2}, Landroid/view/View;->setSelected(Z)V

    const/4 v2, 0x1

    if-eq p1, v2, :cond_2

    const/4 v0, 0x2

    if-eq p1, v0, :cond_1

    const/4 v0, 0x3

    if-eq p1, v0, :cond_0

    goto :goto_1

    :cond_0
    invoke-virtual {p0, v3}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, v2}, Landroid/view/View;->setSelected(Z)V

    invoke-virtual {p0, v3}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1}, Landroid/view/View;->requestFocus()Z

    invoke-virtual {p0, v3}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    goto :goto_0

    :cond_1
    invoke-virtual {p0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, v2}, Landroid/view/View;->setSelected(Z)V

    invoke-virtual {p0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1}, Landroid/view/View;->requestFocus()Z

    invoke-virtual {p0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    goto :goto_0

    :cond_2
    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, v2}, Landroid/view/View;->setSelected(Z)V

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1}, Landroid/view/View;->requestFocus()Z

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    :goto_0
    invoke-virtual {p1}, Landroid/view/View;->requestFocusFromTouch()Z

    :goto_1
    return-void
.end method


# virtual methods
.method public synthetic f(Landroid/view/View;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/PlayerActivity;->e(Landroid/view/View;)V

    return-void
.end method

.method public synthetic h(Landroid/view/View;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/PlayerActivity;->g(Landroid/view/View;)V

    return-void
.end method

.method protected hideBottomUIMenu()V
    .locals 2

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x13

    if-lt v0, v1, :cond_0

    const/16 v0, 0xe06

    goto :goto_0

    :cond_0
    const/16 v0, 0x202

    :goto_0
    invoke-virtual {p0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    move-result-object v1

    invoke-virtual {v1}, Landroid/view/Window;->getDecorView()Landroid/view/View;

    move-result-object v1

    invoke-virtual {v1, v0}, Landroid/view/View;->setSystemUiVisibility(I)V

    return-void
.end method

.method public isControl()Z
    .locals 1

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    invoke-virtual {v0}, Landroid/widget/LinearLayout;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public isRightView()Z
    .locals 1

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    invoke-virtual {v0}, Landroid/widget/LinearLayout;->getVisibility()I

    move-result v0

    if-nez v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public isVip()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public synthetic j(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;Ljava/lang/String;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/salkin/tv/video/PlayerActivity;->i(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;Ljava/lang/String;)V

    return-void
.end method

.method public synthetic l(JI)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Lcom/salkin/tv/video/PlayerActivity;->k(JI)V

    return-void
.end method

.method public synthetic n()V
    .locals 0

    invoke-direct {p0}, Lcom/salkin/tv/video/PlayerActivity;->m()V

    return-void
.end method

.method public onCenterAreaClick()V
    .locals 2

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->startHideHandler()V

    return-void
.end method

.method protected onCreate(Landroid/os/Bundle;)V
    .locals 6
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "MissingInflatedId"
        }
    .end annotation

    invoke-super {p0, p1}, Landroidx/fragment/app/d;->onCreate(Landroid/os/Bundle;)V

    invoke-virtual {p0}, Landroid/app/Activity;->getWindow()Landroid/view/Window;

    move-result-object p1

    const/16 v0, 0x80

    invoke-virtual {p1, v0}, Landroid/view/Window;->addFlags(I)V

    const p1, 0x7f0c001c

    invoke-virtual {p0, p1}, Landroidx/activity/ComponentActivity;->setContentView(I)V

    const p1, 0x7f090061

    invoke-virtual {p0, p1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Lcom/salkin/tv/video/widgets/IjkVideoView;

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mIjkVideoView:Lcom/salkin/tv/video/widgets/IjkVideoView;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/widget/FrameLayout;->setClickable(Z)V

    const v0, 0x7f090079

    invoke-virtual {p0, v0}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    new-instance v2, Lcom/salkin/tv/video/f;

    invoke-direct {v2, p0}, Lcom/salkin/tv/video/f;-><init>(Lcom/salkin/tv/video/PlayerActivity;)V

    invoke-virtual {v0, v2}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    invoke-virtual {p0, p1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    new-instance v0, Lcom/salkin/tv/video/e;

    invoke-direct {v0, p0}, Lcom/salkin/tv/video/e;-><init>(Lcom/salkin/tv/video/PlayerActivity;)V

    invoke-virtual {p1, v0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    const p1, 0x7f09008d

    invoke-virtual {p0, p1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Lcom/salkin/tv/video/views/PlayerControllerView;

    iput-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    const p1, 0x7f09008b

    invoke-virtual {p0, p1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Lcom/salkin/tv/video/views/PlayerRightView;

    iput-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    const p1, 0x7f09008c

    invoke-virtual {p0, p1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Lcom/salkin/tv/video/views/PlayerVipPurchase;

    iput-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerVipPurchase:Lcom/salkin/tv/video/views/PlayerVipPurchase;

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    invoke-virtual {p1, p0}, Lcom/salkin/tv/video/views/PlayerRightView;->setListener(Lcom/salkin/tv/video/views/PlayerRightView$PlayerRightViewInterface;)V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerVipPurchase:Lcom/salkin/tv/video/views/PlayerVipPurchase;

    invoke-virtual {p1, p0}, Lcom/salkin/tv/video/views/PlayerVipPurchase;->setListener(Lcom/salkin/tv/video/views/PlayerVipPurchase$PlayerVipPurchaseInteracface;)V

    invoke-direct {p0}, Lcom/salkin/tv/video/PlayerActivity;->initIjkPlayer()V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-virtual {p1, v0}, Lcom/salkin/tv/video/views/PlayerControllerView;->setPlayer(Lcom/salkin/tv/video/interfaces/PlayerInterface;)V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {p1, v1}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->setAutoPlay(Z)V

    :try_start_0
    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "last_pos"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    if-eqz p1, :cond_0

    invoke-virtual {p1}, Ljava/lang/String;->length()I

    move-result v0

    if-lez v0, :cond_0

    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p1

    const/16 v0, 0x3c

    if-le p1, v0, :cond_0

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0, p1}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->setStartTime(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Exception;->printStackTrace()V

    :cond_0
    :goto_0
    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "movie_data"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Lcom/salkin/tv/video/entity/MovieDataEntity;->fromJson(Ljava/lang/String;)Lcom/salkin/tv/video/entity/MovieDataEntity;

    move-result-object p1

    iput-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    invoke-static {}, Lcom/salkin/tv/video/MyApp;->getInstance()Lcom/salkin/tv/video/MyApp;

    move-result-object p1

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    invoke-virtual {p1, v0}, Lcom/salkin/tv/video/MyApp;->setMovieData(Lcom/salkin/tv/video/entity/MovieDataEntity;)V

    invoke-direct {p0}, Lcom/salkin/tv/video/PlayerActivity;->playItem()V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    invoke-virtual {p1, v0}, Lcom/salkin/tv/video/views/PlayerControllerView;->setMovieData(Lcom/salkin/tv/video/entity/MovieDataEntity;)V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    invoke-virtual {p1, v0}, Lcom/salkin/tv/video/views/PlayerRightView;->setMovieData(Lcom/salkin/tv/video/entity/MovieDataEntity;)V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    const/4 v0, 0x0

    invoke-virtual {p1, v0}, Landroid/widget/LinearLayout;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->startHideHandler()V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    invoke-virtual {p1, p0}, Lcom/salkin/tv/video/views/PlayerControllerView;->setListener(Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;)V

    new-instance v0, Ljava/util/Timer;

    invoke-direct {v0}, Ljava/util/Timer;-><init>()V

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->updateProgressTimer:Ljava/util/Timer;

    iget-object v1, p0, Lcom/salkin/tv/video/PlayerActivity;->timerTask:Ljava/util/TimerTask;

    const-wide/16 v2, 0x2710

    const-wide/16 v4, 0x2710

    invoke-virtual/range {v0 .. v5}, Ljava/util/Timer;->schedule(Ljava/util/TimerTask;JJ)V

    return-void
.end method

.method public onDestroy()V
    .locals 2

    invoke-super {p0}, Landroidx/fragment/app/d;->onDestroy()V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->stop()V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->destroy()V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mHandler:Landroid/os/Handler;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    invoke-direct {p0}, Lcom/salkin/tv/video/PlayerActivity;->clearUIHandler()V

    const/4 v0, 0x0

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mHandler:Landroid/os/Handler;

    :try_start_0
    iget-object v1, p0, Lcom/salkin/tv/video/PlayerActivity;->timerTask:Ljava/util/TimerTask;

    invoke-virtual {v1}, Ljava/util/TimerTask;->cancel()Z

    iput-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->timerTask:Ljava/util/TimerTask;
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception v0

    invoke-virtual {v0}, Ljava/lang/Exception;->printStackTrace()V

    :goto_0
    return-void
.end method

.method public onKeyDown(ILandroid/view/KeyEvent;)Z
    .locals 4

    const/16 v0, 0x8

    const/4 v1, 0x1

    const/4 v2, 0x4

    if-ne p1, v2, :cond_0

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isRightView()Z

    move-result v2

    if-eqz v2, :cond_f

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    invoke-virtual {p1, v0}, Landroid/widget/LinearLayout;->setVisibility(I)V

    return v1

    :cond_0
    const/16 v2, 0x5c

    if-ne p1, v2, :cond_2

    invoke-virtual {p2}, Landroid/view/KeyEvent;->getRepeatCount()I

    move-result v2

    if-nez v2, :cond_2

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isVip()Z

    move-result v1

    if-eqz v1, :cond_1

    invoke-super {p0, p1, p2}, Landroid/app/Activity;->onKeyDown(ILandroid/view/KeyEvent;)Z

    move-result p1

    return p1

    :cond_1
    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isRightView()Z

    move-result v1

    if-eqz v1, :cond_f

    iget-object v1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    invoke-virtual {v1, v0}, Landroid/widget/LinearLayout;->setVisibility(I)V

    goto/16 :goto_2

    :cond_2
    const/16 v2, 0x42

    const/4 v3, 0x0

    if-eq p1, v2, :cond_b

    const/16 v2, 0x17

    if-ne p1, v2, :cond_3

    goto :goto_1

    :cond_3
    const/16 v2, 0xb0

    if-eq p1, v2, :cond_8

    const/16 v2, 0x14

    if-ne p1, v2, :cond_4

    goto :goto_0

    :cond_4
    const/16 v0, 0x16

    if-eq p1, v0, :cond_5

    const/16 v0, 0x15

    if-ne p1, v0, :cond_f

    :cond_5
    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isVip()Z

    move-result p2

    if-eqz p2, :cond_6

    return v1

    :cond_6
    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isRightView()Z

    move-result p2

    if-eqz p2, :cond_7

    return v3

    :cond_7
    iget-object p2, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    invoke-virtual {p2, v3}, Landroid/widget/LinearLayout;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->startHideHandler()V

    iget-object p2, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    invoke-virtual {p2, p1}, Lcom/salkin/tv/video/views/PlayerControllerView;->seekByKeyCode(I)V

    return v1

    :cond_8
    :goto_0
    iget-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    invoke-virtual {v2, v0}, Landroid/widget/LinearLayout;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isVip()Z

    move-result v2

    if-eqz v2, :cond_9

    return v1

    :cond_9
    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isRightView()Z

    move-result v2

    if-eqz v2, :cond_a

    invoke-super {p0, p1, p2}, Landroid/app/Activity;->onKeyDown(ILandroid/view/KeyEvent;)Z

    move-result p1

    return p1

    :cond_a
    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    invoke-virtual {p1}, Landroid/widget/LinearLayout;->clearAnimation()V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    invoke-virtual {p1, v0}, Landroid/widget/LinearLayout;->setVisibility(I)V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    invoke-virtual {p1, v3}, Landroid/widget/LinearLayout;->setVisibility(I)V

    return v1

    :cond_b
    :goto_1
    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isVip()Z

    move-result v0

    if-eqz v0, :cond_c

    return v1

    :cond_c
    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isRightView()Z

    move-result v0

    if-eqz v0, :cond_d

    return v3

    :cond_d
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    invoke-virtual {v0, v3}, Landroid/widget/LinearLayout;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->startHideHandler()V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->isPlaying()Z

    move-result v0

    if-eqz v0, :cond_e

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->pause()V

    goto :goto_2

    :cond_e
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->resume()V

    :cond_f
    :goto_2
    invoke-super {p0, p1, p2}, Landroid/app/Activity;->onKeyDown(ILandroid/view/KeyEvent;)Z

    move-result p1

    return p1
.end method

.method public onPlayCompleted()V
    .locals 4

    invoke-static {}, Lcom/salkin/tv/video/MyApp;->getInstance()Lcom/salkin/tv/video/MyApp;

    move-result-object v0

    invoke-virtual {v0}, Lcom/salkin/tv/video/MyApp;->getPlayingItem()Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;

    move-result-object v0

    const/4 v1, 0x0

    :goto_0
    iget-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    iget-object v2, v2, Lcom/salkin/tv/video/entity/MovieDataEntity;->child_list:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    if-ge v1, v2, :cond_1

    iget-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    iget-object v2, v2, Lcom/salkin/tv/video/entity/MovieDataEntity;->child_list:Ljava/util/List;

    invoke-interface {v2, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;

    iget v3, v0, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;->index:I

    iget v2, v2, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;->index:I

    if-ne v3, v2, :cond_0

    iget-object v2, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    iget-object v2, v2, Lcom/salkin/tv/video/entity/MovieDataEntity;->child_list:Ljava/util/List;

    invoke-interface {v2}, Ljava/util/List;->size()I

    move-result v2

    add-int/lit8 v2, v2, -0x1

    if-ge v1, v2, :cond_0

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    iget-object v0, v0, Lcom/salkin/tv/video/entity/MovieDataEntity;->child_list:Ljava/util/List;

    add-int/lit8 v1, v1, 0x1

    invoke-interface {v0, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;

    invoke-direct {p0, v0}, Lcom/salkin/tv/video/PlayerActivity;->playByIndexString(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;)V

    return-void

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    return-void
.end method

.method public onPlayerSelected(Landroid/view/View;)V
    .locals 0

    invoke-virtual {p1}, Landroid/view/View;->getTag()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p1

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/PlayerActivity;->playByType(I)V

    return-void
.end method

.method public onRightViewJiClick(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/PlayerActivity;->playByIndexString(Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;)V

    return-void
.end method

.method public onRightViewMenuClick()V
    .locals 2

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerRightView:Lcom/salkin/tv/video/views/PlayerRightView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    return-void
.end method

.method public onScreenSelected(Landroid/view/View;)V
    .locals 5

    invoke-virtual {p1}, Landroid/view/View;->getTag()Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/String;

    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "---------tag----"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    const-string v1, "basbas"

    invoke-static {v1, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    new-instance p1, Lcom/salkin/tv/video/widgets/Settings;

    invoke-direct {p1, p0}, Lcom/salkin/tv/video/widgets/Settings;-><init>(Landroid/content/Context;)V

    invoke-virtual {p1, v0}, Lcom/salkin/tv/video/widgets/Settings;->setScreenSizeType(I)V

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {p1, v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->setScreenSizeType(I)V

    const p1, 0x7f090097

    invoke-virtual {p0, p1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v1

    const/4 v2, 0x0

    invoke-virtual {v1, v2}, Landroid/view/View;->setSelected(Z)V

    const v1, 0x7f090098

    invoke-virtual {p0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v3

    invoke-virtual {v3, v2}, Landroid/view/View;->setSelected(Z)V

    const v3, 0x7f090099

    invoke-virtual {p0, v3}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v4

    invoke-virtual {v4, v2}, Landroid/view/View;->setSelected(Z)V

    const/4 v2, 0x1

    if-eq v0, v2, :cond_2

    const/4 p1, 0x2

    if-eq v0, p1, :cond_1

    const/4 p1, 0x3

    if-eq v0, p1, :cond_0

    goto :goto_1

    :cond_0
    invoke-virtual {p0, v3}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, v2}, Landroid/view/View;->setSelected(Z)V

    invoke-virtual {p0, v3}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1}, Landroid/view/View;->requestFocus()Z

    invoke-virtual {p0, v3}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    goto :goto_0

    :cond_1
    invoke-virtual {p0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, v2}, Landroid/view/View;->setSelected(Z)V

    invoke-virtual {p0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1}, Landroid/view/View;->requestFocus()Z

    invoke-virtual {p0, v1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    goto :goto_0

    :cond_2
    invoke-virtual {p0, p1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    invoke-virtual {v0, v2}, Landroid/view/View;->setSelected(Z)V

    invoke-virtual {p0, p1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object v0

    invoke-virtual {v0}, Landroid/view/View;->requestFocus()Z

    invoke-virtual {p0, p1}, Landroid/app/Activity;->findViewById(I)Landroid/view/View;

    move-result-object p1

    :goto_0
    invoke-virtual {p1}, Landroid/view/View;->requestFocusFromTouch()Z

    :goto_1
    return-void
.end method

.method public onSeekbarChanged()V
    .locals 2

    # 无论VIP状态如何，都显示控制界面并启动隐藏定时器
    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/widget/LinearLayout;->setVisibility(I)V

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->startHideHandler()V

    return-void
.end method

.method public onVipClick()V
    .locals 3

    invoke-virtual {p0}, Lcom/salkin/tv/video/PlayerActivity;->isVip()Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Landroid/app/Activity;->finish()V

    invoke-static {}, Lcom/salkin/tv/video/MyApp;->getInstance()Lcom/salkin/tv/video/MyApp;

    move-result-object v0

    invoke-virtual {v0}, Lcom/salkin/tv/video/MyApp;->getChannel()Lk/a/c/a/j;

    move-result-object v0

    const/4 v1, 0x0

    const-string v2, "goToVipPage"

    invoke-virtual {v0, v2, v1}, Lk/a/c/a/j;->c(Ljava/lang/String;Ljava/lang/Object;)V

    :cond_0
    return-void
.end method

.method public showVip()V
    .locals 2

    # 禁用VIP提示，直接返回
    return-void
.end method

.method public startHideHandler()V
    .locals 4

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mHandler:Landroid/os/Handler;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/salkin/tv/video/PlayerActivity;->mHandler:Landroid/os/Handler;

    const-wide/16 v2, 0x1388

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    return-void
.end method
