.class public interface abstract Lm/a/h2;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/s/g$b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<S:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ll/s/g$b;"
    }
.end annotation


# virtual methods
.method public abstract K(Ll/s/g;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g;",
            ")TS;"
        }
    .end annotation
.end method

.method public abstract r(Ll/s/g;Ljava/lang/Object;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g;",
            "TS;)V"
        }
    .end annotation
.end method
