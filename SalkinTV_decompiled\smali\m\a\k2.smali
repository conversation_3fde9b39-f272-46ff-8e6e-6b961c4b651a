.class public final Lm/a/k2;
.super Lm/a/p2/d0;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lm/a/p2/d0<",
        "TT;>;"
    }
.end annotation


# instance fields
.field private d:Ljava/lang/ThreadLocal;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/ThreadLocal<",
            "Ll/i<",
            "Ll/s/g;",
            "Ljava/lang/Object;",
            ">;>;"
        }
    .end annotation
.end field


# virtual methods
.method protected B0(Ljava/lang/Object;)V
    .locals 5

    iget-object v0, p0, Lm/a/k2;->d:Ljava/lang/ThreadLocal;

    invoke-virtual {v0}, Ljava/lang/ThreadLocal;->get()Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Ll/i;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Ll/i;->a()Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Ll/s/g;

    invoke-virtual {v0}, Ll/i;->b()Ljava/lang/Object;

    move-result-object v0

    invoke-static {v2, v0}, Lm/a/p2/j0;->a(Ll/s/g;Ljava/lang/Object;)V

    iget-object v0, p0, Lm/a/k2;->d:Ljava/lang/ThreadLocal;

    invoke-virtual {v0, v1}, Ljava/lang/ThreadLocal;->set(Ljava/lang/Object;)V

    :goto_0
    iget-object v0, p0, Lm/a/p2/d0;->c:Ll/s/d;

    invoke-static {p1, v0}, Lm/a/z;->a(Ljava/lang/Object;Ll/s/d;)Ljava/lang/Object;

    move-result-object p1

    iget-object v0, p0, Lm/a/p2/d0;->c:Ll/s/d;

    invoke-interface {v0}, Ll/s/d;->getContext()Ll/s/g;

    move-result-object v2

    invoke-static {v2, v1}, Lm/a/p2/j0;->c(Ll/s/g;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    sget-object v4, Lm/a/p2/j0;->a:Lm/a/p2/f0;

    if-eq v3, v4, :cond_1

    invoke-static {v0, v2, v3}, Lm/a/c0;->f(Ll/s/d;Ll/s/g;Ljava/lang/Object;)Lm/a/k2;

    move-result-object v1

    :cond_1
    :try_start_0
    iget-object v0, p0, Lm/a/p2/d0;->c:Ll/s/d;

    invoke-interface {v0, p1}, Ll/s/d;->resumeWith(Ljava/lang/Object;)V

    sget-object p1, Ll/p;->a:Ll/p;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v1, :cond_2

    invoke-virtual {v1}, Lm/a/k2;->G0()Z

    move-result p1

    if-eqz p1, :cond_3

    :cond_2
    invoke-static {v2, v3}, Lm/a/p2/j0;->a(Ll/s/g;Ljava/lang/Object;)V

    :cond_3
    return-void

    :catchall_0
    move-exception p1

    if-eqz v1, :cond_4

    invoke-virtual {v1}, Lm/a/k2;->G0()Z

    move-result v0

    if-eqz v0, :cond_5

    :cond_4
    invoke-static {v2, v3}, Lm/a/p2/j0;->a(Ll/s/g;Ljava/lang/Object;)V

    :cond_5
    throw p1
.end method

.method public final G0()Z
    .locals 2

    iget-object v0, p0, Lm/a/k2;->d:Ljava/lang/ThreadLocal;

    invoke-virtual {v0}, Ljava/lang/ThreadLocal;->get()Ljava/lang/Object;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    return v0

    :cond_0
    iget-object v0, p0, Lm/a/k2;->d:Ljava/lang/ThreadLocal;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Ljava/lang/ThreadLocal;->set(Ljava/lang/Object;)V

    const/4 v0, 0x1

    return v0
.end method

.method public final H0(Ll/s/g;Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lm/a/k2;->d:Ljava/lang/ThreadLocal;

    invoke-static {p1, p2}, Ll/m;->a(Ljava/lang/Object;Ljava/lang/Object;)Ll/i;

    move-result-object p1

    invoke-virtual {v0, p1}, Ljava/lang/ThreadLocal;->set(Ljava/lang/Object;)V

    return-void
.end method
