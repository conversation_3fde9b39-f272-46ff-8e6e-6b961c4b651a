.class public final Ll/z/i;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/z/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        "R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ll/z/b<",
        "TR;>;"
    }
.end annotation


# instance fields
.field private final a:Ll/z/b;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/z/b<",
            "TT;>;"
        }
    .end annotation
.end field

.field private final b:Ll/v/c/l;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/v/c/l<",
            "TT;TR;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ll/z/b;Ll/v/c/l;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/z/b<",
            "+TT;>;",
            "Ll/v/c/l<",
            "-TT;+TR;>;)V"
        }
    .end annotation

    const-string v0, "sequence"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "transformer"

    invoke-static {p2, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ll/z/i;->a:Ll/z/b;

    iput-object p2, p0, Ll/z/i;->b:Ll/v/c/l;

    return-void
.end method

.method public static final synthetic a(Ll/z/i;)Ll/z/b;
    .locals 0

    iget-object p0, p0, Ll/z/i;->a:Ll/z/b;

    return-object p0
.end method

.method public static final synthetic b(Ll/z/i;)Ll/v/c/l;
    .locals 0

    iget-object p0, p0, Ll/z/i;->b:Ll/v/c/l;

    return-object p0
.end method


# virtual methods
.method public iterator()Ljava/util/Iterator;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/Iterator<",
            "TR;>;"
        }
    .end annotation

    new-instance v0, Ll/z/i$a;

    invoke-direct {v0, p0}, Ll/z/i$a;-><init>(Ll/z/i;)V

    return-object v0
.end method
