.class public abstract Ln/e0/i/g$h;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ln/e0/i/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "h"
.end annotation


# static fields
.field public static final a:Ln/e0/i/g$h;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ln/e0/i/g$h$a;

    invoke-direct {v0}, Ln/e0/i/g$h$a;-><init>()V

    sput-object v0, Ln/e0/i/g$h;->a:Ln/e0/i/g$h;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Ln/e0/i/g;)V
    .locals 0

    return-void
.end method

.method public abstract b(Ln/e0/i/i;)V
.end method
