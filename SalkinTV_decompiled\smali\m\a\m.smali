.class public final Lm/a/m;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final a:Lm/a/p2/f0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lm/a/p2/f0;

    const-string v1, "RESUME_TOKEN"

    invoke-direct {v0, v1}, Lm/a/p2/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lm/a/m;->a:Lm/a/p2/f0;

    return-void
.end method
