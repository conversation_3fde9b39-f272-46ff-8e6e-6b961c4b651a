.class Lm/a/g2;
.super Lm/a/a;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lm/a/a<",
        "Ll/p;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>(Ll/s/g;Z)V
    .locals 1

    const/4 v0, 0x1

    invoke-direct {p0, p1, v0, p2}, Lm/a/a;-><init>(Ll/s/g;ZZ)V

    return-void
.end method


# virtual methods
.method protected X(Ljava/lang/Throwable;)Z
    .locals 1

    invoke-virtual {p0}, Lm/a/a;->getContext()Ll/s/g;

    move-result-object v0

    invoke-static {v0, p1}, Lm/a/g0;->a(Ll/s/g;Ljava/lang/Throwable;)V

    const/4 p1, 0x1

    return p1
.end method
