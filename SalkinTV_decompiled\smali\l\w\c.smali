.class public abstract Ll/w/c;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ll/w/c$a;
    }
.end annotation


# static fields
.field public static final a:Ll/w/c$a;

.field private static final b:Ll/w/c;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Ll/w/c$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Ll/w/c$a;-><init>(Ll/v/d/e;)V

    sput-object v0, Ll/w/c;->a:Ll/w/c$a;

    sget-object v0, Ll/t/b;->a:Ll/t/a;

    invoke-virtual {v0}, Ll/t/a;->b()Ll/w/c;

    move-result-object v0

    sput-object v0, Ll/w/c;->b:Ll/w/c;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static final synthetic a()Ll/w/c;
    .locals 1

    sget-object v0, Ll/w/c;->b:Ll/w/c;

    return-object v0
.end method


# virtual methods
.method public abstract b()I
.end method
