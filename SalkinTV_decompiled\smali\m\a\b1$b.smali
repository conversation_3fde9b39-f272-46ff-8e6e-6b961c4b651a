.class public final Lm/a/b1$b;
.super Lm/a/p2/k0;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lm/a/b1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lm/a/p2/k0<",
        "Lm/a/b1$a;",
        ">;"
    }
.end annotation


# instance fields
.field public b:J


# direct methods
.method public constructor <init>(J)V
    .locals 0

    invoke-direct {p0}, Lm/a/p2/k0;-><init>()V

    iput-wide p1, p0, Lm/a/b1$b;->b:J

    return-void
.end method
