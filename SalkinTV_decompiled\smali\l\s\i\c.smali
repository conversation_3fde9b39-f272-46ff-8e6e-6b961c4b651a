.class Ll/s/i/c;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public static a(Ll/v/c/p;Ljava/lang/Object;Ll/s/d;)Ll/s/d;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            "T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/v/c/p<",
            "-TR;-",
            "Ll/s/d<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;TR;",
            "Ll/s/d<",
            "-TT;>;)",
            "Ll/s/d<",
            "Ll/p;",
            ">;"
        }
    .end annotation

    const-string v0, "<this>"

    invoke-static {p0, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "completion"

    invoke-static {p2, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p2}, Ll/s/j/a/h;->a(Ll/s/d;)Ll/s/d;

    instance-of v0, p0, Ll/s/j/a/a;

    if-eqz v0, :cond_0

    check-cast p0, Ll/s/j/a/a;

    invoke-virtual {p0, p1, p2}, Ll/s/j/a/a;->create(Ljava/lang/Object;Ll/s/d;)Ll/s/d;

    move-result-object p0

    goto :goto_0

    :cond_0
    invoke-interface {p2}, Ll/s/d;->getContext()Ll/s/g;

    move-result-object v0

    sget-object v1, Ll/s/h;->a:Ll/s/h;

    if-ne v0, v1, :cond_1

    new-instance v0, Ll/s/i/c$a;

    invoke-direct {v0, p2, p0, p1}, Ll/s/i/c$a;-><init>(Ll/s/d;Ll/v/c/p;Ljava/lang/Object;)V

    move-object p0, v0

    goto :goto_0

    :cond_1
    new-instance v1, Ll/s/i/c$b;

    invoke-direct {v1, p2, v0, p0, p1}, Ll/s/i/c$b;-><init>(Ll/s/d;Ll/s/g;Ll/v/c/p;Ljava/lang/Object;)V

    move-object p0, v1

    :goto_0
    return-object p0
.end method

.method public static b(Ll/s/d;)Ll/s/d;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/s/d<",
            "-TT;>;)",
            "Ll/s/d<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "<this>"

    invoke-static {p0, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    instance-of v0, p0, Ll/s/j/a/d;

    if-eqz v0, :cond_0

    move-object v0, p0

    check-cast v0, Ll/s/j/a/d;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_2

    invoke-virtual {v0}, Ll/s/j/a/d;->intercepted()Ll/s/d;

    move-result-object v0

    if-nez v0, :cond_1

    goto :goto_1

    :cond_1
    move-object p0, v0

    :cond_2
    :goto_1
    return-object p0
.end method
