.class public Lm/a/l;
.super Lm/a/u0;
.source ""

# interfaces
.implements Lm/a/k;
.implements Ll/s/j/a/e;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lm/a/u0<",
        "TT;>;",
        "Lm/a/k<",
        "TT;>;",
        "Ll/s/j/a/e;"
    }
.end annotation


# static fields
.field private static final synthetic g:Ljava/util/concurrent/atomic/AtomicIntegerFieldUpdater;

.field private static final synthetic h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;


# instance fields
.field private volatile synthetic _decision:I

.field private volatile synthetic _state:Ljava/lang/Object;

.field private final d:Ll/s/d;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/s/d<",
            "TT;>;"
        }
    .end annotation
.end field

.field private final e:Ll/s/g;

.field private f:Lm/a/y0;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    const-class v0, Lm/a/l;

    const-string v1, "_decision"

    invoke-static {v0, v1}, Ljava/util/concurrent/atomic/AtomicIntegerFieldUpdater;->newUpdater(Ljava/lang/Class;Ljava/lang/String;)Ljava/util/concurrent/atomic/AtomicIntegerFieldUpdater;

    move-result-object v0

    sput-object v0, Lm/a/l;->g:Ljava/util/concurrent/atomic/AtomicIntegerFieldUpdater;

    const-class v0, Lm/a/l;

    const-class v1, Ljava/lang/Object;

    const-string v2, "_state"

    invoke-static {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->newUpdater(Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    move-result-object v0

    sput-object v0, Lm/a/l;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    return-void
.end method

.method public constructor <init>(Ll/s/d;I)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/d<",
            "-TT;>;I)V"
        }
    .end annotation

    invoke-direct {p0, p2}, Lm/a/u0;-><init>(I)V

    iput-object p1, p0, Lm/a/l;->d:Ll/s/d;

    invoke-static {}, Lm/a/n0;->a()Z

    move-result v0

    const/4 v1, 0x0

    if-eqz v0, :cond_2

    const/4 v0, -0x1

    if-eq p2, v0, :cond_0

    const/4 p2, 0x1

    goto :goto_0

    :cond_0
    const/4 p2, 0x0

    :goto_0
    if-eqz p2, :cond_1

    goto :goto_1

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_2
    :goto_1
    invoke-interface {p1}, Ll/s/d;->getContext()Ll/s/g;

    move-result-object p1

    iput-object p1, p0, Lm/a/l;->e:Ll/s/g;

    iput v1, p0, Lm/a/l;->_decision:I

    sget-object p1, Lm/a/d;->a:Lm/a/d;

    iput-object p1, p0, Lm/a/l;->_state:Ljava/lang/Object;

    return-void
.end method

.method private final A()Z
    .locals 1

    iget v0, p0, Lm/a/u0;->c:I

    invoke-static {v0}, Lm/a/v0;->c(I)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Lm/a/l;->d:Ll/s/d;

    check-cast v0, Lm/a/p2/i;

    invoke-virtual {v0}, Lm/a/p2/i;->m()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method private final B(Ll/v/c/l;)Lm/a/i;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)",
            "Lm/a/i;"
        }
    .end annotation

    instance-of v0, p1, Lm/a/i;

    if-eqz v0, :cond_0

    check-cast p1, Lm/a/i;

    goto :goto_0

    :cond_0
    new-instance v0, Lm/a/n1;

    invoke-direct {v0, p1}, Lm/a/n1;-><init>(Ll/v/c/l;)V

    move-object p1, v0

    :goto_0
    return-object p1
.end method

.method private final C(Ll/v/c/l;Ljava/lang/Object;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;",
            "Ljava/lang/Object;",
            ")V"
        }
    .end annotation

    new-instance v0, Ljava/lang/IllegalStateException;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "It\'s prohibited to register multiple handlers, tried to register "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string p1, ", already has "

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private final F()V
    .locals 3

    iget-object v0, p0, Lm/a/l;->d:Ll/s/d;

    instance-of v1, v0, Lm/a/p2/i;

    const/4 v2, 0x0

    if-eqz v1, :cond_0

    check-cast v0, Lm/a/p2/i;

    goto :goto_0

    :cond_0
    move-object v0, v2

    :goto_0
    if-nez v0, :cond_1

    goto :goto_1

    :cond_1
    invoke-virtual {v0, p0}, Lm/a/p2/i;->s(Lm/a/k;)Ljava/lang/Throwable;

    move-result-object v2

    :goto_1
    if-nez v2, :cond_2

    return-void

    :cond_2
    invoke-virtual {p0}, Lm/a/l;->s()V

    invoke-virtual {p0, v2}, Lm/a/l;->p(Ljava/lang/Throwable;)Z

    return-void
.end method

.method private final H(Ljava/lang/Object;ILl/v/c/l;)V
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "I",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)V"
        }
    .end annotation

    :goto_0
    iget-object v0, p0, Lm/a/l;->_state:Ljava/lang/Object;

    instance-of v1, v0, Lm/a/d2;

    if-eqz v1, :cond_1

    move-object v3, v0

    check-cast v3, Lm/a/d2;

    const/4 v7, 0x0

    move-object v2, p0

    move-object v4, p1

    move v5, p2

    move-object v6, p3

    invoke-direct/range {v2 .. v7}, Lm/a/l;->J(Lm/a/d2;Ljava/lang/Object;ILl/v/c/l;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    sget-object v2, Lm/a/l;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v2, p0, v0, v1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-direct {p0}, Lm/a/l;->t()V

    invoke-direct {p0, p2}, Lm/a/l;->u(I)V

    return-void

    :cond_1
    instance-of p2, v0, Lm/a/o;

    if-eqz p2, :cond_3

    check-cast v0, Lm/a/o;

    invoke-virtual {v0}, Lm/a/o;->c()Z

    move-result p2

    if-eqz p2, :cond_3

    if-nez p3, :cond_2

    goto :goto_1

    :cond_2
    iget-object p1, v0, Lm/a/v;->a:Ljava/lang/Throwable;

    invoke-virtual {p0, p3, p1}, Lm/a/l;->m(Ll/v/c/l;Ljava/lang/Throwable;)V

    :goto_1
    return-void

    :cond_3
    invoke-direct {p0, p1}, Lm/a/l;->j(Ljava/lang/Object;)Ljava/lang/Void;

    const/4 p1, 0x0

    goto :goto_3

    :goto_2
    throw p1

    :goto_3
    goto :goto_2
.end method

.method static synthetic I(Lm/a/l;Ljava/lang/Object;ILl/v/c/l;ILjava/lang/Object;)V
    .locals 0

    if-nez p5, :cond_1

    and-int/lit8 p4, p4, 0x4

    if-eqz p4, :cond_0

    const/4 p3, 0x0

    :cond_0
    invoke-direct {p0, p1, p2, p3}, Lm/a/l;->H(Ljava/lang/Object;ILl/v/c/l;)V

    return-void

    :cond_1
    new-instance p0, Ljava/lang/UnsupportedOperationException;

    const-string p1, "Super calls with default arguments not supported in this target, function: resumeImpl"

    invoke-direct {p0, p1}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method private final J(Lm/a/d2;Ljava/lang/Object;ILl/v/c/l;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lm/a/d2;",
            "Ljava/lang/Object;",
            "I",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;",
            "Ljava/lang/Object;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    instance-of v0, p2, Lm/a/v;

    if-eqz v0, :cond_5

    invoke-static {}, Lm/a/n0;->a()Z

    move-result p1

    const/4 p3, 0x1

    const/4 v0, 0x0

    if-eqz p1, :cond_2

    if-nez p5, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_2
    :goto_1
    invoke-static {}, Lm/a/n0;->a()Z

    move-result p1

    if-eqz p1, :cond_a

    if-nez p4, :cond_3

    goto :goto_2

    :cond_3
    const/4 p3, 0x0

    :goto_2
    if-eqz p3, :cond_4

    goto :goto_4

    :cond_4
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_5
    invoke-static {p3}, Lm/a/v0;->b(I)Z

    move-result p3

    if-nez p3, :cond_6

    if-nez p5, :cond_6

    goto :goto_4

    :cond_6
    if-nez p4, :cond_8

    instance-of p3, p1, Lm/a/i;

    if-eqz p3, :cond_7

    instance-of p3, p1, Lm/a/e;

    if-eqz p3, :cond_8

    :cond_7
    if-eqz p5, :cond_a

    :cond_8
    new-instance p3, Lm/a/u;

    instance-of v0, p1, Lm/a/i;

    if-eqz v0, :cond_9

    check-cast p1, Lm/a/i;

    goto :goto_3

    :cond_9
    const/4 p1, 0x0

    :goto_3
    move-object v2, p1

    const/4 v5, 0x0

    const/16 v6, 0x10

    const/4 v7, 0x0

    move-object v0, p3

    move-object v1, p2

    move-object v3, p4

    move-object v4, p5

    invoke-direct/range {v0 .. v7}, Lm/a/u;-><init>(Ljava/lang/Object;Lm/a/i;Ll/v/c/l;Ljava/lang/Object;Ljava/lang/Throwable;ILl/v/d/e;)V

    move-object p2, p3

    :cond_a
    :goto_4
    return-object p2
.end method

.method private final K()Z
    .locals 4

    :cond_0
    iget v0, p0, Lm/a/l;->_decision:I

    const/4 v1, 0x0

    const/4 v2, 0x1

    if-eqz v0, :cond_2

    if-ne v0, v2, :cond_1

    return v1

    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "Already resumed"

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_2
    sget-object v0, Lm/a/l;->g:Ljava/util/concurrent/atomic/AtomicIntegerFieldUpdater;

    const/4 v3, 0x2

    invoke-virtual {v0, p0, v1, v3}, Ljava/util/concurrent/atomic/AtomicIntegerFieldUpdater;->compareAndSet(Ljava/lang/Object;II)Z

    move-result v0

    if-eqz v0, :cond_0

    return v2
.end method

.method private final L(Ljava/lang/Object;Ljava/lang/Object;Ll/v/c/l;)Lm/a/p2/f0;
    .locals 8
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)",
            "Lm/a/p2/f0;"
        }
    .end annotation

    :goto_0
    iget-object v0, p0, Lm/a/l;->_state:Ljava/lang/Object;

    instance-of v1, v0, Lm/a/d2;

    if-eqz v1, :cond_1

    move-object v3, v0

    check-cast v3, Lm/a/d2;

    iget v5, p0, Lm/a/u0;->c:I

    move-object v2, p0

    move-object v4, p1

    move-object v6, p3

    move-object v7, p2

    invoke-direct/range {v2 .. v7}, Lm/a/l;->J(Lm/a/d2;Ljava/lang/Object;ILl/v/c/l;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    sget-object v2, Lm/a/l;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v2, p0, v0, v1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-direct {p0}, Lm/a/l;->t()V

    sget-object p1, Lm/a/m;->a:Lm/a/p2/f0;

    return-object p1

    :cond_1
    instance-of p3, v0, Lm/a/u;

    const/4 v1, 0x0

    if-eqz p3, :cond_4

    if-eqz p2, :cond_4

    check-cast v0, Lm/a/u;

    iget-object p3, v0, Lm/a/u;->d:Ljava/lang/Object;

    if-ne p3, p2, :cond_4

    invoke-static {}, Lm/a/n0;->a()Z

    move-result p2

    if-eqz p2, :cond_3

    iget-object p2, v0, Lm/a/u;->a:Ljava/lang/Object;

    invoke-static {p2, p1}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_1

    :cond_2
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_3
    :goto_1
    sget-object v1, Lm/a/m;->a:Lm/a/p2/f0;

    :cond_4
    return-object v1
.end method

.method private final M()Z
    .locals 3

    :cond_0
    iget v0, p0, Lm/a/l;->_decision:I

    const/4 v1, 0x0

    if-eqz v0, :cond_2

    const/4 v2, 0x2

    if-ne v0, v2, :cond_1

    return v1

    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "Already suspended"

    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_2
    sget-object v0, Lm/a/l;->g:Ljava/util/concurrent/atomic/AtomicIntegerFieldUpdater;

    const/4 v2, 0x1

    invoke-virtual {v0, p0, v1, v2}, Ljava/util/concurrent/atomic/AtomicIntegerFieldUpdater;->compareAndSet(Ljava/lang/Object;II)Z

    move-result v0

    if-eqz v0, :cond_0

    return v2
.end method

.method private final j(Ljava/lang/Object;)Ljava/lang/Void;
    .locals 2

    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "Already resumed, but proposed with update "

    invoke-static {v1, p1}, Ll/v/d/j;->j(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p1

    invoke-direct {v0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method private final k(Ll/v/c/l;Ljava/lang/Throwable;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;",
            "Ljava/lang/Throwable;",
            ")V"
        }
    .end annotation

    :try_start_0
    invoke-interface {p1, p2}, Ll/v/c/l;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    invoke-virtual {p0}, Lm/a/l;->getContext()Ll/s/g;

    move-result-object p2

    new-instance v0, Lm/a/y;

    const-string v1, "Exception in invokeOnCancellation handler for "

    invoke-static {v1, p0}, Ll/v/d/j;->j(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1, p1}, Lm/a/y;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    invoke-static {p2, v0}, Lm/a/g0;->a(Ll/s/g;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method

.method private final r(Ljava/lang/Throwable;)Z
    .locals 1

    invoke-direct {p0}, Lm/a/l;->A()Z

    move-result v0

    if-nez v0, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    iget-object v0, p0, Lm/a/l;->d:Ll/s/d;

    check-cast v0, Lm/a/p2/i;

    invoke-virtual {v0, p1}, Lm/a/p2/i;->p(Ljava/lang/Throwable;)Z

    move-result p1

    return p1
.end method

.method private final t()V
    .locals 1

    invoke-direct {p0}, Lm/a/l;->A()Z

    move-result v0

    if-nez v0, :cond_0

    invoke-virtual {p0}, Lm/a/l;->s()V

    :cond_0
    return-void
.end method

.method private final u(I)V
    .locals 1

    invoke-direct {p0}, Lm/a/l;->K()Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-static {p0, p1}, Lm/a/v0;->a(Lm/a/u0;I)V

    return-void
.end method

.method private final y()Ljava/lang/String;
    .locals 2

    invoke-virtual {p0}, Lm/a/l;->x()Ljava/lang/Object;

    move-result-object v0

    instance-of v1, v0, Lm/a/d2;

    if-eqz v1, :cond_0

    const-string v0, "Active"

    goto :goto_0

    :cond_0
    instance-of v0, v0, Lm/a/o;

    if-eqz v0, :cond_1

    const-string v0, "Cancelled"

    goto :goto_0

    :cond_1
    const-string v0, "Completed"

    :goto_0
    return-object v0
.end method

.method private final z()Lm/a/y0;
    .locals 7

    invoke-virtual {p0}, Lm/a/l;->getContext()Ll/s/g;

    move-result-object v0

    sget-object v1, Lm/a/q1;->H:Lm/a/q1$b;

    invoke-interface {v0, v1}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object v0

    move-object v1, v0

    check-cast v1, Lm/a/q1;

    if-nez v1, :cond_0

    const/4 v0, 0x0

    return-object v0

    :cond_0
    const/4 v2, 0x1

    const/4 v3, 0x0

    new-instance v4, Lm/a/p;

    invoke-direct {v4, p0}, Lm/a/p;-><init>(Lm/a/l;)V

    const/4 v5, 0x2

    const/4 v6, 0x0

    invoke-static/range {v1 .. v6}, Lm/a/q1$a;->d(Lm/a/q1;ZZLl/v/c/l;ILjava/lang/Object;)Lm/a/y0;

    move-result-object v0

    iput-object v0, p0, Lm/a/l;->f:Lm/a/y0;

    return-object v0
.end method


# virtual methods
.method protected D()Ljava/lang/String;
    .locals 1

    const-string v0, "CancellableContinuation"

    return-object v0
.end method

.method public final E(Ljava/lang/Throwable;)V
    .locals 1

    invoke-direct {p0, p1}, Lm/a/l;->r(Ljava/lang/Throwable;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0, p1}, Lm/a/l;->p(Ljava/lang/Throwable;)Z

    invoke-direct {p0}, Lm/a/l;->t()V

    return-void
.end method

.method public final G()Z
    .locals 4

    invoke-static {}, Lm/a/n0;->a()Z

    move-result v0

    const/4 v1, 0x1

    const/4 v2, 0x0

    if-eqz v0, :cond_2

    iget v0, p0, Lm/a/u0;->c:I

    const/4 v3, 0x2

    if-ne v0, v3, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    if-eqz v0, :cond_1

    goto :goto_1

    :cond_1
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_2
    :goto_1
    invoke-static {}, Lm/a/n0;->a()Z

    move-result v0

    if-eqz v0, :cond_5

    iget-object v0, p0, Lm/a/l;->f:Lm/a/y0;

    sget-object v3, Lm/a/c2;->a:Lm/a/c2;

    if-eq v0, v3, :cond_3

    const/4 v0, 0x1

    goto :goto_2

    :cond_3
    const/4 v0, 0x0

    :goto_2
    if-eqz v0, :cond_4

    goto :goto_3

    :cond_4
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_5
    :goto_3
    iget-object v0, p0, Lm/a/l;->_state:Ljava/lang/Object;

    invoke-static {}, Lm/a/n0;->a()Z

    move-result v3

    if-eqz v3, :cond_7

    instance-of v3, v0, Lm/a/d2;

    xor-int/2addr v3, v1

    if-eqz v3, :cond_6

    goto :goto_4

    :cond_6
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_7
    :goto_4
    instance-of v3, v0, Lm/a/u;

    if-eqz v3, :cond_8

    check-cast v0, Lm/a/u;

    iget-object v0, v0, Lm/a/u;->d:Ljava/lang/Object;

    if-eqz v0, :cond_8

    invoke-virtual {p0}, Lm/a/l;->s()V

    return v2

    :cond_8
    iput v2, p0, Lm/a/l;->_decision:I

    sget-object v0, Lm/a/d;->a:Lm/a/d;

    iput-object v0, p0, Lm/a/l;->_state:Ljava/lang/Object;

    return v1
.end method

.method public a(Ljava/lang/Object;Ljava/lang/Throwable;)V
    .locals 10

    :cond_0
    iget-object p1, p0, Lm/a/l;->_state:Ljava/lang/Object;

    instance-of v0, p1, Lm/a/d2;

    if-nez v0, :cond_4

    instance-of v0, p1, Lm/a/v;

    if-eqz v0, :cond_1

    return-void

    :cond_1
    instance-of v0, p1, Lm/a/u;

    if-eqz v0, :cond_3

    move-object v0, p1

    check-cast v0, Lm/a/u;

    invoke-virtual {v0}, Lm/a/u;->c()Z

    move-result v1

    xor-int/lit8 v1, v1, 0x1

    if-eqz v1, :cond_2

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/16 v7, 0xf

    const/4 v8, 0x0

    move-object v1, v0

    move-object v6, p2

    invoke-static/range {v1 .. v8}, Lm/a/u;->b(Lm/a/u;Ljava/lang/Object;Lm/a/i;Ll/v/c/l;Ljava/lang/Object;Ljava/lang/Throwable;ILjava/lang/Object;)Lm/a/u;

    move-result-object v1

    sget-object v2, Lm/a/l;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v2, p0, p1, v1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    invoke-virtual {v0, p0, p2}, Lm/a/u;->d(Lm/a/l;Ljava/lang/Throwable;)V

    return-void

    :cond_2
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Must be called at most once"

    invoke-virtual {p2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    sget-object v8, Lm/a/l;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    new-instance v9, Lm/a/u;

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/16 v6, 0xe

    const/4 v7, 0x0

    move-object v0, v9

    move-object v1, p1

    move-object v5, p2

    invoke-direct/range {v0 .. v7}, Lm/a/u;-><init>(Ljava/lang/Object;Lm/a/i;Ll/v/c/l;Ljava/lang/Object;Ljava/lang/Throwable;ILl/v/d/e;)V

    invoke-virtual {v8, p0, p1, v9}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    return-void

    :cond_4
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "Not completed"

    invoke-virtual {p2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    goto :goto_1

    :goto_0
    throw p1

    :goto_1
    goto :goto_0
.end method

.method public final b()Ll/s/d;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ll/s/d<",
            "TT;>;"
        }
    .end annotation

    iget-object v0, p0, Lm/a/l;->d:Ll/s/d;

    return-object v0
.end method

.method public c(Ljava/lang/Object;Ljava/lang/Object;Ll/v/c/l;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;",
            "Ljava/lang/Object;",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    invoke-direct {p0, p1, p2, p3}, Lm/a/l;->L(Ljava/lang/Object;Ljava/lang/Object;Ll/v/c/l;)Lm/a/p2/f0;

    move-result-object p1

    return-object p1
.end method

.method public d(Ljava/lang/Object;)Ljava/lang/Throwable;
    .locals 2

    invoke-super {p0, p1}, Lm/a/u0;->d(Ljava/lang/Object;)Ljava/lang/Throwable;

    move-result-object p1

    if-nez p1, :cond_0

    const/4 p1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Lm/a/l;->b()Ll/s/d;

    move-result-object v0

    invoke-static {}, Lm/a/n0;->d()Z

    move-result v1

    if-eqz v1, :cond_2

    instance-of v1, v0, Ll/s/j/a/e;

    if-nez v1, :cond_1

    goto :goto_0

    :cond_1
    check-cast v0, Ll/s/j/a/e;

    invoke-static {p1, v0}, Lm/a/p2/e0;->a(Ljava/lang/Throwable;Ll/s/j/a/e;)Ljava/lang/Throwable;

    move-result-object p1

    :cond_2
    :goto_0
    return-object p1
.end method

.method public e(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;",
            "Ljava/lang/Object;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation

    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, v0}, Lm/a/l;->L(Ljava/lang/Object;Ljava/lang/Object;Ll/v/c/l;)Lm/a/p2/f0;

    move-result-object p1

    return-object p1
.end method

.method public f(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/lang/Object;",
            ")TT;"
        }
    .end annotation

    instance-of v0, p1, Lm/a/u;

    if-eqz v0, :cond_0

    check-cast p1, Lm/a/u;

    iget-object p1, p1, Lm/a/u;->a:Ljava/lang/Object;

    :cond_0
    return-object p1
.end method

.method public getCallerFrame()Ll/s/j/a/e;
    .locals 2

    iget-object v0, p0, Lm/a/l;->d:Ll/s/d;

    instance-of v1, v0, Ll/s/j/a/e;

    if-eqz v1, :cond_0

    check-cast v0, Ll/s/j/a/e;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return-object v0
.end method

.method public getContext()Ll/s/g;
    .locals 1

    iget-object v0, p0, Lm/a/l;->e:Ll/s/g;

    return-object v0
.end method

.method public getStackTraceElement()Ljava/lang/StackTraceElement;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public h(Ljava/lang/Object;Ll/v/c/l;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)V"
        }
    .end annotation

    iget v0, p0, Lm/a/u0;->c:I

    invoke-direct {p0, p1, v0, p2}, Lm/a/l;->H(Ljava/lang/Object;ILl/v/c/l;)V

    return-void
.end method

.method public i()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, Lm/a/l;->x()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public final l(Lm/a/i;Ljava/lang/Throwable;)V
    .locals 2

    :try_start_0
    invoke-virtual {p1, p2}, Lm/a/j;->a(Ljava/lang/Throwable;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    invoke-virtual {p0}, Lm/a/l;->getContext()Ll/s/g;

    move-result-object p2

    new-instance v0, Lm/a/y;

    const-string v1, "Exception in invokeOnCancellation handler for "

    invoke-static {v1, p0}, Ll/v/d/j;->j(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1, p1}, Lm/a/y;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    invoke-static {p2, v0}, Lm/a/g0;->a(Ll/s/g;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method

.method public final m(Ll/v/c/l;Ljava/lang/Throwable;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;",
            "Ljava/lang/Throwable;",
            ")V"
        }
    .end annotation

    :try_start_0
    invoke-interface {p1, p2}, Ll/v/c/l;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :catchall_0
    move-exception p1

    invoke-virtual {p0}, Lm/a/l;->getContext()Ll/s/g;

    move-result-object p2

    new-instance v0, Lm/a/y;

    const-string v1, "Exception in resume onCancellation handler for "

    invoke-static {v1, p0}, Ll/v/d/j;->j(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1, p1}, Lm/a/y;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    invoke-static {p2, v0}, Lm/a/g0;->a(Ll/s/g;Ljava/lang/Throwable;)V

    :goto_0
    return-void
.end method

.method public n(Ll/v/c/l;)V
    .locals 11
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0, p1}, Lm/a/l;->B(Ll/v/c/l;)Lm/a/i;

    move-result-object v8

    :cond_0
    iget-object v9, p0, Lm/a/l;->_state:Ljava/lang/Object;

    instance-of v0, v9, Lm/a/d;

    if-eqz v0, :cond_1

    sget-object v0, Lm/a/l;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v0, p0, v9, v8}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_1
    instance-of v0, v9, Lm/a/i;

    const/4 v1, 0x0

    if-nez v0, :cond_c

    instance-of v0, v9, Lm/a/v;

    if-eqz v0, :cond_6

    move-object v2, v9

    check-cast v2, Lm/a/v;

    invoke-virtual {v2}, Lm/a/v;->b()Z

    move-result v3

    if-eqz v3, :cond_5

    instance-of v3, v9, Lm/a/o;

    if-eqz v3, :cond_4

    if-eqz v0, :cond_2

    goto :goto_0

    :cond_2
    move-object v2, v1

    :goto_0
    if-nez v2, :cond_3

    goto :goto_1

    :cond_3
    iget-object v1, v2, Lm/a/v;->a:Ljava/lang/Throwable;

    :goto_1
    invoke-direct {p0, p1, v1}, Lm/a/l;->k(Ll/v/c/l;Ljava/lang/Throwable;)V

    :cond_4
    return-void

    :cond_5
    invoke-direct {p0, p1, v9}, Lm/a/l;->C(Ll/v/c/l;Ljava/lang/Object;)V

    throw v1

    :cond_6
    instance-of v0, v9, Lm/a/u;

    if-eqz v0, :cond_a

    move-object v0, v9

    check-cast v0, Lm/a/u;

    iget-object v2, v0, Lm/a/u;->b:Lm/a/i;

    if-nez v2, :cond_9

    instance-of v1, v8, Lm/a/e;

    if-eqz v1, :cond_7

    return-void

    :cond_7
    invoke-virtual {v0}, Lm/a/u;->c()Z

    move-result v1

    if-eqz v1, :cond_8

    iget-object v0, v0, Lm/a/u;->e:Ljava/lang/Throwable;

    invoke-direct {p0, p1, v0}, Lm/a/l;->k(Ll/v/c/l;Ljava/lang/Throwable;)V

    return-void

    :cond_8
    const/4 v1, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/16 v6, 0x1d

    const/4 v7, 0x0

    move-object v2, v8

    invoke-static/range {v0 .. v7}, Lm/a/u;->b(Lm/a/u;Ljava/lang/Object;Lm/a/i;Ll/v/c/l;Ljava/lang/Object;Ljava/lang/Throwable;ILjava/lang/Object;)Lm/a/u;

    move-result-object v0

    sget-object v1, Lm/a/l;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v1, p0, v9, v0}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_9
    invoke-direct {p0, p1, v9}, Lm/a/l;->C(Ll/v/c/l;Ljava/lang/Object;)V

    throw v1

    :cond_a
    instance-of v0, v8, Lm/a/e;

    if-eqz v0, :cond_b

    return-void

    :cond_b
    new-instance v10, Lm/a/u;

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    const/16 v6, 0x1c

    const/4 v7, 0x0

    move-object v0, v10

    move-object v1, v9

    move-object v2, v8

    invoke-direct/range {v0 .. v7}, Lm/a/u;-><init>(Ljava/lang/Object;Lm/a/i;Ll/v/c/l;Ljava/lang/Object;Ljava/lang/Throwable;ILl/v/d/e;)V

    sget-object v0, Lm/a/l;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v0, p0, v9, v10}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-void

    :cond_c
    invoke-direct {p0, p1, v9}, Lm/a/l;->C(Ll/v/c/l;Ljava/lang/Object;)V

    goto :goto_3

    :goto_2
    throw v1

    :goto_3
    goto :goto_2
.end method

.method public o(Ljava/lang/Throwable;)Ljava/lang/Object;
    .locals 4

    new-instance v0, Lm/a/v;

    const/4 v1, 0x0

    const/4 v2, 0x2

    const/4 v3, 0x0

    invoke-direct {v0, p1, v1, v2, v3}, Lm/a/v;-><init>(Ljava/lang/Throwable;ZILl/v/d/e;)V

    invoke-direct {p0, v0, v3, v3}, Lm/a/l;->L(Ljava/lang/Object;Ljava/lang/Object;Ll/v/c/l;)Lm/a/p2/f0;

    move-result-object p1

    return-object p1
.end method

.method public p(Ljava/lang/Throwable;)Z
    .locals 4

    :goto_0
    iget-object v0, p0, Lm/a/l;->_state:Ljava/lang/Object;

    instance-of v1, v0, Lm/a/d2;

    if-nez v1, :cond_0

    const/4 p1, 0x0

    return p1

    :cond_0
    new-instance v1, Lm/a/o;

    instance-of v2, v0, Lm/a/i;

    invoke-direct {v1, p0, p1, v2}, Lm/a/o;-><init>(Ll/s/d;Ljava/lang/Throwable;Z)V

    sget-object v3, Lm/a/l;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v3, p0, v0, v1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-nez v1, :cond_1

    goto :goto_0

    :cond_1
    if-eqz v2, :cond_2

    check-cast v0, Lm/a/i;

    goto :goto_1

    :cond_2
    const/4 v0, 0x0

    :goto_1
    if-nez v0, :cond_3

    goto :goto_2

    :cond_3
    invoke-virtual {p0, v0, p1}, Lm/a/l;->l(Lm/a/i;Ljava/lang/Throwable;)V

    :goto_2
    invoke-direct {p0}, Lm/a/l;->t()V

    iget p1, p0, Lm/a/u0;->c:I

    invoke-direct {p0, p1}, Lm/a/l;->u(I)V

    const/4 p1, 0x1

    return p1
.end method

.method public q(Ljava/lang/Object;)V
    .locals 1

    invoke-static {}, Lm/a/n0;->a()Z

    move-result v0

    if-eqz v0, :cond_2

    sget-object v0, Lm/a/m;->a:Lm/a/p2/f0;

    if-ne p1, v0, :cond_0

    const/4 p1, 0x1

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    if-eqz p1, :cond_1

    goto :goto_1

    :cond_1
    new-instance p1, Ljava/lang/AssertionError;

    invoke-direct {p1}, Ljava/lang/AssertionError;-><init>()V

    throw p1

    :cond_2
    :goto_1
    iget p1, p0, Lm/a/u0;->c:I

    invoke-direct {p0, p1}, Lm/a/l;->u(I)V

    return-void
.end method

.method public resumeWith(Ljava/lang/Object;)V
    .locals 6

    invoke-static {p1, p0}, Lm/a/z;->c(Ljava/lang/Object;Lm/a/k;)Ljava/lang/Object;

    move-result-object v1

    iget v2, p0, Lm/a/u0;->c:I

    const/4 v3, 0x0

    const/4 v4, 0x4

    const/4 v5, 0x0

    move-object v0, p0

    invoke-static/range {v0 .. v5}, Lm/a/l;->I(Lm/a/l;Ljava/lang/Object;ILl/v/c/l;ILjava/lang/Object;)V

    return-void
.end method

.method public final s()V
    .locals 1

    iget-object v0, p0, Lm/a/l;->f:Lm/a/y0;

    if-nez v0, :cond_0

    return-void

    :cond_0
    invoke-interface {v0}, Lm/a/y0;->a()V

    sget-object v0, Lm/a/c2;->a:Lm/a/c2;

    iput-object v0, p0, Lm/a/l;->f:Lm/a/y0;

    return-void
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Lm/a/l;->D()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x28

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lm/a/l;->d:Ll/s/d;

    invoke-static {v1}, Lm/a/o0;->c(Ll/s/d;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "){"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-direct {p0}, Lm/a/l;->y()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "}@"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static {p0}, Lm/a/o0;->b(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public v(Lm/a/q1;)Ljava/lang/Throwable;
    .locals 0

    invoke-interface {p1}, Lm/a/q1;->D()Ljava/util/concurrent/CancellationException;

    move-result-object p1

    return-object p1
.end method

.method public final w()Ljava/lang/Object;
    .locals 3

    invoke-direct {p0}, Lm/a/l;->A()Z

    move-result v0

    invoke-direct {p0}, Lm/a/l;->M()Z

    move-result v1

    if-eqz v1, :cond_2

    iget-object v1, p0, Lm/a/l;->f:Lm/a/y0;

    if-nez v1, :cond_0

    invoke-direct {p0}, Lm/a/l;->z()Lm/a/y0;

    :cond_0
    if-eqz v0, :cond_1

    invoke-direct {p0}, Lm/a/l;->F()V

    :cond_1
    invoke-static {}, Ll/s/i/b;->c()Ljava/lang/Object;

    move-result-object v0

    return-object v0

    :cond_2
    if-eqz v0, :cond_3

    invoke-direct {p0}, Lm/a/l;->F()V

    :cond_3
    invoke-virtual {p0}, Lm/a/l;->x()Ljava/lang/Object;

    move-result-object v0

    instance-of v1, v0, Lm/a/v;

    if-eqz v1, :cond_5

    check-cast v0, Lm/a/v;

    iget-object v0, v0, Lm/a/v;->a:Ljava/lang/Throwable;

    invoke-static {}, Lm/a/n0;->d()Z

    move-result v1

    if-eqz v1, :cond_4

    invoke-static {v0, p0}, Lm/a/p2/e0;->a(Ljava/lang/Throwable;Ll/s/j/a/e;)Ljava/lang/Throwable;

    move-result-object v0

    :cond_4
    throw v0

    :cond_5
    iget v1, p0, Lm/a/u0;->c:I

    invoke-static {v1}, Lm/a/v0;->b(I)Z

    move-result v1

    if-eqz v1, :cond_7

    invoke-virtual {p0}, Lm/a/l;->getContext()Ll/s/g;

    move-result-object v1

    sget-object v2, Lm/a/q1;->H:Lm/a/q1$b;

    invoke-interface {v1, v2}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object v1

    check-cast v1, Lm/a/q1;

    if-eqz v1, :cond_7

    invoke-interface {v1}, Lm/a/q1;->b()Z

    move-result v2

    if-nez v2, :cond_7

    invoke-interface {v1}, Lm/a/q1;->D()Ljava/util/concurrent/CancellationException;

    move-result-object v1

    invoke-virtual {p0, v0, v1}, Lm/a/l;->a(Ljava/lang/Object;Ljava/lang/Throwable;)V

    invoke-static {}, Lm/a/n0;->d()Z

    move-result v0

    if-eqz v0, :cond_6

    invoke-static {v1, p0}, Lm/a/p2/e0;->a(Ljava/lang/Throwable;Ll/s/j/a/e;)Ljava/lang/Throwable;

    move-result-object v1

    :cond_6
    throw v1

    :cond_7
    invoke-virtual {p0, v0}, Lm/a/l;->f(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method

.method public final x()Ljava/lang/Object;
    .locals 1

    iget-object v0, p0, Lm/a/l;->_state:Ljava/lang/Object;

    return-object v0
.end method
