.class Ll/z/f;
.super Ll/z/e;
.source ""


# direct methods
.method public static a(Ljava/util/Iterator;)Ll/z/b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/util/Iterator<",
            "+TT;>;)",
            "Ll/z/b<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "<this>"

    invoke-static {p0, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Ll/z/f$a;

    invoke-direct {v0, p0}, Ll/z/f$a;-><init>(Ljava/util/Iterator;)V

    invoke-static {v0}, Ll/z/f;->b(Ll/z/b;)Ll/z/b;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Ll/z/b;)Ll/z/b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/z/b<",
            "+TT;>;)",
            "Ll/z/b<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "<this>"

    invoke-static {p0, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    instance-of v0, p0, Ll/z/a;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Ll/z/a;

    invoke-direct {v0, p0}, Ll/z/a;-><init>(Ll/z/b;)V

    move-object p0, v0

    :goto_0
    return-object p0
.end method
