.class public final Ll/z/c;
.super Ll/z/h;
.source ""


# direct methods
.method public static bridge synthetic a(Ljava/util/Iterator;)Ll/z/b;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ljava/util/Iterator<",
            "+TT;>;)",
            "Ll/z/b<",
            "TT;>;"
        }
    .end annotation

    invoke-static {p0}, Ll/z/f;->a(Ljava/util/Iterator;)Ll/z/b;

    move-result-object p0

    return-object p0
.end method

.method public static bridge synthetic c(Ll/z/b;Ll/v/c/l;)Ll/z/b;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            "R:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/z/b<",
            "+TT;>;",
            "Ll/v/c/l<",
            "-TT;+TR;>;)",
            "Ll/z/b<",
            "TR;>;"
        }
    .end annotation

    invoke-static {p0, p1}, Ll/z/h;->c(Ll/z/b;Ll/v/c/l;)Ll/z/b;

    move-result-object p0

    return-object p0
.end method

.method public static bridge synthetic e(Ll/z/b;)Ljava/util/List;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/z/b<",
            "+TT;>;)",
            "Ljava/util/List<",
            "TT;>;"
        }
    .end annotation

    invoke-static {p0}, Ll/z/h;->e(Ll/z/b;)Ljava/util/List;

    move-result-object p0

    return-object p0
.end method
