.class public final Lm/a/f;
.super Lm/a/b1;
.source ""


# instance fields
.field private final g:Ljava/lang/Thread;


# direct methods
.method public constructor <init>(Ljava/lang/Thread;)V
    .locals 0

    invoke-direct {p0}, Lm/a/b1;-><init>()V

    iput-object p1, p0, Lm/a/f;->g:Ljava/lang/Thread;

    return-void
.end method


# virtual methods
.method protected f0()Ljava/lang/Thread;
    .locals 1

    iget-object v0, p0, Lm/a/f;->g:Ljava/lang/Thread;

    return-object v0
.end method
