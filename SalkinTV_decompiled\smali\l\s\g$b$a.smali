.class public final Ll/s/g$b$a;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll/s/g$b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method public static a(Ll/s/g$b;Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/s/g$b;",
            "TR;",
            "Ll/v/c/p<",
            "-TR;-",
            "Ll/s/g$b;",
            "+TR;>;)TR;"
        }
    .end annotation

    const-string v0, "operation"

    invoke-static {p2, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p2, p1, p0}, Ll/v/c/p;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p0

    return-object p0
.end method

.method public static b(Ll/s/g$b;Ll/s/g$c;)Ll/s/g$b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E::",
            "Ll/s/g$b;",
            ">(",
            "Ll/s/g$b;",
            "Ll/s/g$c<",
            "TE;>;)TE;"
        }
    .end annotation

    const-string v0, "key"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p0}, Ll/s/g$b;->getKey()Ll/s/g$c;

    move-result-object v0

    invoke-static {v0, p1}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const-string p1, "null cannot be cast to non-null type E of kotlin.coroutines.CoroutineContext.Element.get"

    invoke-static {p0, p1}, Ll/v/d/j;->c(Ljava/lang/Object;Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    const/4 p0, 0x0

    :goto_0
    return-object p0
.end method

.method public static c(Ll/s/g$b;Ll/s/g$c;)Ll/s/g;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g$b;",
            "Ll/s/g$c<",
            "*>;)",
            "Ll/s/g;"
        }
    .end annotation

    const-string v0, "key"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-interface {p0}, Ll/s/g$b;->getKey()Ll/s/g$c;

    move-result-object v0

    invoke-static {v0, p1}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    sget-object p0, Ll/s/h;->a:Ll/s/h;

    :cond_0
    return-object p0
.end method

.method public static d(Ll/s/g$b;Ll/s/g;)Ll/s/g;
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p0, p1}, Ll/s/g$a;->a(Ll/s/g;Ll/s/g;)Ll/s/g;

    move-result-object p0

    return-object p0
.end method
