.class public final Lm/a/c2;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lm/a/y0;
.implements Lm/a/q;


# static fields
.field public static final a:Lm/a/c2;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/c2;

    invoke-direct {v0}, Lm/a/c2;-><init>()V

    sput-object v0, Lm/a/c2;->a:Lm/a/c2;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a()V
    .locals 0

    return-void
.end method

.method public g(Ljava/lang/Throwable;)Z
    .locals 0

    const/4 p1, 0x0

    return p1
.end method

.method public getParent()Lm/a/q1;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    const-string v0, "NonDisposableHandle"

    return-object v0
.end method
