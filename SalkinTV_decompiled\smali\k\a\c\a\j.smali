.class public Lk/a/c/a/j;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lk/a/c/a/j$a;,
        Lk/a/c/a/j$b;,
        Lk/a/c/a/j$d;,
        Lk/a/c/a/j$c;
    }
.end annotation


# instance fields
.field private final a:Lk/a/c/a/c;

.field private final b:Ljava/lang/String;

.field private final c:Lk/a/c/a/k;

.field private final d:Lk/a/c/a/c$c;


# direct methods
.method public constructor <init>(Lk/a/c/a/c;Ljava/lang/String;)V
    .locals 1

    sget-object v0, Lk/a/c/a/q;->b:Lk/a/c/a/q;

    invoke-direct {p0, p1, p2, v0}, Lk/a/c/a/j;-><init>(Lk/a/c/a/c;Ljava/lang/String;Lk/a/c/a/k;)V

    return-void
.end method

.method public constructor <init>(Lk/a/c/a/c;Ljava/lang/String;Lk/a/c/a/k;)V
    .locals 1

    const/4 v0, 0x0

    invoke-direct {p0, p1, p2, p3, v0}, Lk/a/c/a/j;-><init>(Lk/a/c/a/c;Ljava/lang/String;Lk/a/c/a/k;Lk/a/c/a/c$c;)V

    return-void
.end method

.method public constructor <init>(Lk/a/c/a/c;Ljava/lang/String;Lk/a/c/a/k;Lk/a/c/a/c$c;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lk/a/c/a/j;->a:Lk/a/c/a/c;

    iput-object p2, p0, Lk/a/c/a/j;->b:Ljava/lang/String;

    iput-object p3, p0, Lk/a/c/a/j;->c:Lk/a/c/a/k;

    iput-object p4, p0, Lk/a/c/a/j;->d:Lk/a/c/a/c$c;

    return-void
.end method

.method static synthetic a(Lk/a/c/a/j;)Lk/a/c/a/k;
    .locals 0

    iget-object p0, p0, Lk/a/c/a/j;->c:Lk/a/c/a/k;

    return-object p0
.end method

.method static synthetic b(Lk/a/c/a/j;)Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lk/a/c/a/j;->b:Ljava/lang/String;

    return-object p0
.end method


# virtual methods
.method public c(Ljava/lang/String;Ljava/lang/Object;)V
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, p2, v0}, Lk/a/c/a/j;->d(Ljava/lang/String;Ljava/lang/Object;Lk/a/c/a/j$d;)V

    return-void
.end method

.method public d(Ljava/lang/String;Ljava/lang/Object;Lk/a/c/a/j$d;)V
    .locals 4

    iget-object v0, p0, Lk/a/c/a/j;->a:Lk/a/c/a/c;

    iget-object v1, p0, Lk/a/c/a/j;->b:Ljava/lang/String;

    iget-object v2, p0, Lk/a/c/a/j;->c:Lk/a/c/a/k;

    new-instance v3, Lk/a/c/a/i;

    invoke-direct {v3, p1, p2}, Lk/a/c/a/i;-><init>(Ljava/lang/String;Ljava/lang/Object;)V

    invoke-interface {v2, v3}, Lk/a/c/a/k;->f(Lk/a/c/a/i;)Ljava/nio/ByteBuffer;

    move-result-object p1

    if-nez p3, :cond_0

    const/4 p2, 0x0

    goto :goto_0

    :cond_0
    new-instance p2, Lk/a/c/a/j$b;

    invoke-direct {p2, p0, p3}, Lk/a/c/a/j$b;-><init>(Lk/a/c/a/j;Lk/a/c/a/j$d;)V

    :goto_0
    invoke-interface {v0, v1, p1, p2}, Lk/a/c/a/c;->b(Ljava/lang/String;Ljava/nio/ByteBuffer;Lk/a/c/a/c$b;)V

    return-void
.end method

.method public e(Lk/a/c/a/j$c;)V
    .locals 4

    iget-object v0, p0, Lk/a/c/a/j;->d:Lk/a/c/a/c$c;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    iget-object v2, p0, Lk/a/c/a/j;->a:Lk/a/c/a/c;

    iget-object v3, p0, Lk/a/c/a/j;->b:Ljava/lang/String;

    if-nez p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance v1, Lk/a/c/a/j$a;

    invoke-direct {v1, p0, p1}, Lk/a/c/a/j$a;-><init>(Lk/a/c/a/j;Lk/a/c/a/j$c;)V

    :goto_0
    invoke-interface {v2, v3, v1, v0}, Lk/a/c/a/c;->g(Ljava/lang/String;Lk/a/c/a/c$a;Lk/a/c/a/c$c;)V

    goto :goto_2

    :cond_1
    iget-object v0, p0, Lk/a/c/a/j;->a:Lk/a/c/a/c;

    iget-object v2, p0, Lk/a/c/a/j;->b:Ljava/lang/String;

    if-nez p1, :cond_2

    goto :goto_1

    :cond_2
    new-instance v1, Lk/a/c/a/j$a;

    invoke-direct {v1, p0, p1}, Lk/a/c/a/j$a;-><init>(Lk/a/c/a/j;Lk/a/c/a/j$c;)V

    :goto_1
    invoke-interface {v0, v2, v1}, Lk/a/c/a/c;->c(Ljava/lang/String;Lk/a/c/a/c$a;)V

    :goto_2
    return-void
.end method
