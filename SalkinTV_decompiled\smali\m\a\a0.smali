.class public interface abstract Lm/a/a0;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lm/a/h2;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<S:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lm/a/h2<",
        "TS;>;"
    }
.end annotation


# virtual methods
.method public abstract m(Ll/s/g$b;)Ll/s/g;
.end method

.method public abstract p()Lm/a/a0;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lm/a/a0<",
            "TS;>;"
        }
    .end annotation
.end method
