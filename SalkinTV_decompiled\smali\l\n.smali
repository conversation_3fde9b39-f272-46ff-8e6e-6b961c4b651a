.class public final Ll/n;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final a:Ll/n;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ll/n;

    invoke-direct {v0}, Ll/n;-><init>()V

    sput-object v0, Ll/n;->a:Ll/n;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
