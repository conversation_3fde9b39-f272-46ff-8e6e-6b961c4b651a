.class public final Lm/a/k0;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public static final a(Ll/s/g;)Lm/a/j0;
    .locals 3

    new-instance v0, Lm/a/p2/g;

    sget-object v1, Lm/a/q1;->H:Lm/a/q1$b;

    invoke-interface {p0, v1}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object v1

    if-eqz v1, :cond_0

    goto :goto_0

    :cond_0
    const/4 v1, 0x1

    const/4 v2, 0x0

    invoke-static {v2, v1, v2}, Lm/a/u1;->b(Lm/a/q1;ILjava/lang/Object;)Lm/a/t;

    move-result-object v1

    invoke-interface {p0, v1}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p0

    :goto_0
    invoke-direct {v0, p0}, Lm/a/p2/g;-><init>(Ll/s/g;)V

    return-object v0
.end method

.method public static final b(Ll/v/c/p;Ll/s/d;)Ljava/lang/Object;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/v/c/p<",
            "-",
            "Lm/a/j0;",
            "-",
            "Ll/s/d<",
            "-TR;>;+",
            "Ljava/lang/Object;",
            ">;",
            "Ll/s/d<",
            "-TR;>;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    new-instance v0, Lm/a/p2/d0;

    invoke-interface {p1}, Ll/s/d;->getContext()Ll/s/g;

    move-result-object v1

    invoke-direct {v0, v1, p1}, Lm/a/p2/d0;-><init>(Ll/s/g;Ll/s/d;)V

    invoke-static {v0, v0, p0}, Lm/a/q2/b;->b(Lm/a/p2/d0;Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;

    move-result-object p0

    invoke-static {}, Ll/s/i/b;->c()Ljava/lang/Object;

    move-result-object v0

    if-ne p0, v0, :cond_0

    invoke-static {p1}, Ll/s/j/a/h;->c(Ll/s/d;)V

    :cond_0
    return-object p0
.end method
