.class final Lm/a/p1;
.super Lm/a/w1;
.source ""


# instance fields
.field private final e:Ll/v/c/l;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/v/c/l<",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ll/v/c/l;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)V"
        }
    .end annotation

    invoke-direct {p0}, Lm/a/w1;-><init>()V

    iput-object p1, p0, Lm/a/p1;->e:Ll/v/c/l;

    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ljava/lang/Throwable;

    invoke-virtual {p0, p1}, Lm/a/p1;->z(Ljava/lang/Throwable;)V

    sget-object p1, Ll/p;->a:Ll/p;

    return-object p1
.end method

.method public z(Ljava/lang/Throwable;)V
    .locals 1

    iget-object v0, p0, Lm/a/p1;->e:Ll/v/c/l;

    invoke-interface {v0, p1}, Ll/v/c/l;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    return-void
.end method
