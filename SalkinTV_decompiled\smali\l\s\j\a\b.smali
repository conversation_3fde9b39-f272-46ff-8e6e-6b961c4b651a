.class public final Ll/s/j/a/b;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public static final a(Z)Ljava/lang/Bo<PERSON>;
    .locals 0

    invoke-static {p0}, <PERSON><PERSON><PERSON>/lang/Bo<PERSON>;->valueOf(Z)Ljava/lang/<PERSON>;

    move-result-object p0

    return-object p0
.end method

.method public static final b(I)Ljava/lang/Integer;
    .locals 1

    new-instance v0, <PERSON><PERSON><PERSON>/lang/Integer;

    invoke-direct {v0, p0}, <PERSON>ja<PERSON>/lang/Integer;-><init>(I)V

    return-object v0
.end method
