.class Lcom/umeng/analytics/pro/bp$b;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/umeng/analytics/pro/bp;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "b"
.end annotation


# static fields
.field public static final a:B = 0x1t

.field public static final b:B = 0x2t

.field public static final c:B = 0x3t

.field public static final d:B = 0x4t

.field public static final e:B = 0x5t

.field public static final f:B = 0x6t

.field public static final g:B = 0x7t

.field public static final h:B = 0x8t

.field public static final i:B = 0x9t

.field public static final j:B = 0xat

.field public static final k:B = 0xbt

.field public static final l:B = 0xct


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
