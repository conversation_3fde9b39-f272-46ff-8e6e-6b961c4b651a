.class public final Lm/a/d0$a;
.super Ll/s/b;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lm/a/d0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ll/s/b<",
        "Ll/s/e;",
        "Lm/a/d0;",
        ">;"
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 2

    sget-object v0, Ll/s/e;->F:Ll/s/e$b;

    sget-object v1, Lm/a/d0$a$a;->a:Lm/a/d0$a$a;

    invoke-direct {p0, v0, v1}, Ll/s/b;-><init>(Ll/s/g$c;Ll/v/c/l;)V

    return-void
.end method

.method public synthetic constructor <init>(Ll/v/d/e;)V
    .locals 0

    invoke-direct {p0}, Lm/a/d0$a;-><init>()V

    return-void
.end method
