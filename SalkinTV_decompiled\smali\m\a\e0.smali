.class public interface abstract Lm/a/e0;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/s/g$b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lm/a/e0$a;
    }
.end annotation


# static fields
.field public static final G:Lm/a/e0$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Lm/a/e0$a;->a:Lm/a/e0$a;

    sput-object v0, Lm/a/e0;->G:Lm/a/e0$a;

    return-void
.end method


# virtual methods
.method public abstract handleException(Ll/s/g;Ljava/lang/Throwable;)V
.end method
