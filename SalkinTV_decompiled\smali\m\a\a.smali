.class public abstract Lm/a/a;
.super Lm/a/x1;
.source ""

# interfaces
.implements Lm/a/q1;
.implements Ll/s/d;
.implements Lm/a/j0;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lm/a/x1;",
        "Lm/a/q1;",
        "Ll/s/d<",
        "TT;>;",
        "Lm/a/j0;"
    }
.end annotation


# instance fields
.field private final b:Ll/s/g;


# direct methods
.method public constructor <init>(Ll/s/g;ZZ)V
    .locals 0

    invoke-direct {p0, p3}, Lm/a/x1;-><init>(Z)V

    if-eqz p2, :cond_0

    sget-object p2, Lm/a/q1;->H:Lm/a/q1$b;

    invoke-interface {p1, p2}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object p2

    check-cast p2, Lm/a/q1;

    invoke-virtual {p0, p2}, Lm/a/x1;->Z(Lm/a/q1;)V

    :cond_0
    invoke-interface {p1, p0}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p1

    iput-object p1, p0, Lm/a/a;->b:Ll/s/g;

    return-void
.end method


# virtual methods
.method protected B0(Ljava/lang/Object;)V
    .locals 0

    invoke-virtual {p0, p1}, Lm/a/x1;->w(Ljava/lang/Object;)V

    return-void
.end method

.method protected C0(Ljava/lang/Throwable;Z)V
    .locals 0

    return-void
.end method

.method protected D0(Ljava/lang/Object;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;)V"
        }
    .end annotation

    return-void
.end method

.method public final E0(Lm/a/l0;Ljava/lang/Object;Ll/v/c/p;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            ">(",
            "Lm/a/l0;",
            "TR;",
            "Ll/v/c/p<",
            "-TR;-",
            "Ll/s/d<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;)V"
        }
    .end annotation

    invoke-virtual {p1, p3, p2, p0}, Lm/a/l0;->b(Ll/v/c/p;Ljava/lang/Object;Ll/s/d;)V

    return-void
.end method

.method protected G()Ljava/lang/String;
    .locals 2

    invoke-static {p0}, Lm/a/o0;->a(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    const-string v1, " was cancelled"

    invoke-static {v0, v1}, Ll/v/d/j;->j(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final Y(Ljava/lang/Throwable;)V
    .locals 1

    iget-object v0, p0, Lm/a/a;->b:Ll/s/g;

    invoke-static {v0, p1}, Lm/a/g0;->a(Ll/s/g;Ljava/lang/Throwable;)V

    return-void
.end method

.method public b()Z
    .locals 1

    invoke-super {p0}, Lm/a/x1;->b()Z

    move-result v0

    return v0
.end method

.method public g()Ll/s/g;
    .locals 1

    iget-object v0, p0, Lm/a/a;->b:Ll/s/g;

    return-object v0
.end method

.method public g0()Ljava/lang/String;
    .locals 3

    iget-object v0, p0, Lm/a/a;->b:Ll/s/g;

    invoke-static {v0}, Lm/a/c0;->b(Ll/s/g;)Ljava/lang/String;

    move-result-object v0

    if-nez v0, :cond_0

    invoke-super {p0}, Lm/a/x1;->g0()Ljava/lang/String;

    move-result-object v0

    return-object v0

    :cond_0
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const/16 v2, 0x22

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v0, "\":"

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-super {p0}, Lm/a/x1;->g0()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public final getContext()Ll/s/g;
    .locals 1

    iget-object v0, p0, Lm/a/a;->b:Ll/s/g;

    return-object v0
.end method

.method protected final l0(Ljava/lang/Object;)V
    .locals 1

    instance-of v0, p1, Lm/a/v;

    if-eqz v0, :cond_0

    check-cast p1, Lm/a/v;

    iget-object v0, p1, Lm/a/v;->a:Ljava/lang/Throwable;

    invoke-virtual {p1}, Lm/a/v;->a()Z

    move-result p1

    invoke-virtual {p0, v0, p1}, Lm/a/a;->C0(Ljava/lang/Throwable;Z)V

    goto :goto_0

    :cond_0
    invoke-virtual {p0, p1}, Lm/a/a;->D0(Ljava/lang/Object;)V

    :goto_0
    return-void
.end method

.method public final resumeWith(Ljava/lang/Object;)V
    .locals 2

    const/4 v0, 0x0

    const/4 v1, 0x1

    invoke-static {p1, v0, v1, v0}, Lm/a/z;->d(Ljava/lang/Object;Ll/v/c/l;ILjava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    invoke-virtual {p0, p1}, Lm/a/x1;->e0(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    sget-object v0, Lm/a/y1;->b:Lm/a/p2/f0;

    if-ne p1, v0, :cond_0

    return-void

    :cond_0
    invoke-virtual {p0, p1}, Lm/a/a;->B0(Ljava/lang/Object;)V

    return-void
.end method
