.class public Lm/a/p2/d0;
.super Lm/a/a;
.source ""

# interfaces
.implements Ll/s/j/a/e;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lm/a/a<",
        "TT;>;",
        "Ll/s/j/a/e;"
    }
.end annotation


# instance fields
.field public final c:Ll/s/d;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/s/d<",
            "TT;>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ll/s/g;Ll/s/d;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g;",
            "Ll/s/d<",
            "-TT;>;)V"
        }
    .end annotation

    const/4 v0, 0x1

    invoke-direct {p0, p1, v0, v0}, Lm/a/a;-><init>(Ll/s/g;ZZ)V

    iput-object p2, p0, Lm/a/p2/d0;->c:Ll/s/d;

    return-void
.end method


# virtual methods
.method protected B0(Ljava/lang/Object;)V
    .locals 1

    iget-object v0, p0, Lm/a/p2/d0;->c:Ll/s/d;

    invoke-static {p1, v0}, Lm/a/z;->a(Ljava/lang/Object;Ll/s/d;)Ljava/lang/Object;

    move-result-object p1

    invoke-interface {v0, p1}, Ll/s/d;->resumeWith(Ljava/lang/Object;)V

    return-void
.end method

.method public final F0()Lm/a/q1;
    .locals 1

    invoke-virtual {p0}, Lm/a/x1;->V()Lm/a/q;

    move-result-object v0

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-interface {v0}, Lm/a/q;->getParent()Lm/a/q1;

    move-result-object v0

    :goto_0
    return-object v0
.end method

.method protected final c0()Z
    .locals 1

    const/4 v0, 0x1

    return v0
.end method

.method public final getCallerFrame()Ll/s/j/a/e;
    .locals 2

    iget-object v0, p0, Lm/a/p2/d0;->c:Ll/s/d;

    instance-of v1, v0, Ll/s/j/a/e;

    if-eqz v1, :cond_0

    check-cast v0, Ll/s/j/a/e;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return-object v0
.end method

.method public final getStackTraceElement()Ljava/lang/StackTraceElement;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method protected w(Ljava/lang/Object;)V
    .locals 3

    iget-object v0, p0, Lm/a/p2/d0;->c:Ll/s/d;

    invoke-static {v0}, Ll/s/i/b;->b(Ll/s/d;)Ll/s/d;

    move-result-object v0

    iget-object v1, p0, Lm/a/p2/d0;->c:Ll/s/d;

    invoke-static {p1, v1}, Lm/a/z;->a(Ljava/lang/Object;Ll/s/d;)Ljava/lang/Object;

    move-result-object p1

    const/4 v1, 0x0

    const/4 v2, 0x2

    invoke-static {v0, p1, v1, v2, v1}, Lm/a/p2/j;->c(Ll/s/d;Ljava/lang/Object;Ll/v/c/l;ILjava/lang/Object;)V

    return-void
.end method
