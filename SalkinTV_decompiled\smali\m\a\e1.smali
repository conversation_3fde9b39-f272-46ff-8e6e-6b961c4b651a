.class public final Lm/a/e1;
.super Ljava/lang/Object;
.source ""


# static fields
.field private static final a:Lm/a/p2/f0;

.field private static final b:Lm/a/p2/f0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lm/a/p2/f0;

    const-string v1, "REMOVED_TASK"

    invoke-direct {v0, v1}, Lm/a/p2/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lm/a/e1;->a:Lm/a/p2/f0;

    new-instance v0, Lm/a/p2/f0;

    const-string v1, "CLOSED_EMPTY"

    invoke-direct {v0, v1}, Lm/a/p2/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lm/a/e1;->b:Lm/a/p2/f0;

    return-void
.end method

.method public static final synthetic a()Lm/a/p2/f0;
    .locals 1

    sget-object v0, Lm/a/e1;->b:Lm/a/p2/f0;

    return-object v0
.end method

.method public static final synthetic b()Lm/a/p2/f0;
    .locals 1

    sget-object v0, Lm/a/e1;->a:Lm/a/p2/f0;

    return-object v0
.end method
