.class final Lm/a/p2/j0$c;
.super Ll/v/d/k;
.source ""

# interfaces
.implements Ll/v/c/p;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm/a/p2/j0;-><clinit>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ll/v/d/k;",
        "Ll/v/c/p<",
        "Lm/a/p2/m0;",
        "Ll/s/g$b;",
        "Lm/a/p2/m0;",
        ">;"
    }
.end annotation


# static fields
.field public static final a:Lm/a/p2/j0$c;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/p2/j0$c;

    invoke-direct {v0}, Lm/a/p2/j0$c;-><init>()V

    sput-object v0, Lm/a/p2/j0$c;->a:Lm/a/p2/j0$c;

    return-void
.end method

.method constructor <init>()V
    .locals 1

    const/4 v0, 0x2

    invoke-direct {p0, v0}, Ll/v/d/k;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final a(Lm/a/p2/m0;Ll/s/g$b;)Lm/a/p2/m0;
    .locals 1

    instance-of v0, p2, Lm/a/h2;

    if-eqz v0, :cond_0

    check-cast p2, Lm/a/h2;

    iget-object v0, p1, Lm/a/p2/m0;->a:Ll/s/g;

    invoke-interface {p2, v0}, Lm/a/h2;->K(Ll/s/g;)Ljava/lang/Object;

    move-result-object v0

    invoke-virtual {p1, p2, v0}, Lm/a/p2/m0;->a(Lm/a/h2;Ljava/lang/Object;)V

    :cond_0
    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lm/a/p2/m0;

    check-cast p2, Ll/s/g$b;

    invoke-virtual {p0, p1, p2}, Lm/a/p2/j0$c;->a(Lm/a/p2/m0;Ll/s/g$b;)Lm/a/p2/m0;

    return-object p1
.end method
