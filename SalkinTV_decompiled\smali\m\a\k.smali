.class public interface abstract Lm/a/k;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/s/d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lm/a/k$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ll/s/d<",
        "TT;>;"
    }
.end annotation


# virtual methods
.method public abstract c(Ljava/lang/Object;Ljava/lang/Object;Ll/v/c/l;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;",
            "Ljava/lang/Object;",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract e(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;",
            "Ljava/lang/Object;",
            ")",
            "Ljava/lang/Object;"
        }
    .end annotation
.end method

.method public abstract h(Ljava/lang/Object;Ll/v/c/l;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TT;",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract n(Ll/v/c/l;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract o(Ljava/lang/Throwable;)Ljava/lang/Object;
.end method

.method public abstract q(Ljava/lang/Object;)V
.end method
