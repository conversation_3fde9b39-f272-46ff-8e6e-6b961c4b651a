.class public interface abstract Ltv/danmaku/ijk/media/exo/demo/player/DemoPlayer$RendererBuilder;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ltv/danmaku/ijk/media/exo/demo/player/DemoPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "RendererBuilder"
.end annotation


# virtual methods
.method public abstract buildRenderers(Ltv/danmaku/ijk/media/exo/demo/player/DemoPlayer;)V
.end method

.method public abstract cancel()V
.end method
