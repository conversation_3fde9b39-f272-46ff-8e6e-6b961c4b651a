.class public interface abstract Ltv/danmaku/ijk/media/player/IMediaPlayer$OnBufferingUpdateListener;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ltv/danmaku/ijk/media/player/IMediaPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnBufferingUpdateListener"
.end annotation


# virtual methods
.method public abstract onBufferingUpdate(Ltv/danmaku/ijk/media/player/IMediaPlayer;I)V
.end method
