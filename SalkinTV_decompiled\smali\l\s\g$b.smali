.class public interface abstract Ll/s/g$b;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/s/g;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll/s/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "b"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ll/s/g$b$a;
    }
.end annotation


# virtual methods
.method public abstract get(Ll/s/g$c;)Ll/s/g$b;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E::",
            "Ll/s/g$b;",
            ">(",
            "Ll/s/g$c<",
            "TE;>;)TE;"
        }
    .end annotation
.end method

.method public abstract getKey()Ll/s/g$c;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ll/s/g$c<",
            "*>;"
        }
    .end annotation
.end method
