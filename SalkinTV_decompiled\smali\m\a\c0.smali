.class public final Lm/a/c0;
.super Ljava/lang/Object;
.source ""


# direct methods
.method private static final a(Ll/s/g;Ll/s/g;Z)Ll/s/g;
    .locals 3

    invoke-static {p0}, Lm/a/c0;->c(Ll/s/g;)Z

    move-result v0

    invoke-static {p1}, Lm/a/c0;->c(Ll/s/g;)Z

    move-result v1

    if-nez v0, :cond_0

    if-nez v1, :cond_0

    invoke-interface {p0, p1}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p0

    return-object p0

    :cond_0
    new-instance v0, Ll/v/d/q;

    invoke-direct {v0}, Ll/v/d/q;-><init>()V

    iput-object p1, v0, Ll/v/d/q;->a:Ljava/lang/Object;

    sget-object p1, Ll/s/h;->a:Ll/s/h;

    new-instance v2, Lm/a/c0$b;

    invoke-direct {v2, v0, p2}, Lm/a/c0$b;-><init>(Ll/v/d/q;Z)V

    invoke-interface {p0, p1, v2}, Ll/s/g;->fold(Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ll/s/g;

    if-eqz v1, :cond_1

    iget-object p2, v0, Ll/v/d/q;->a:Ljava/lang/Object;

    check-cast p2, Ll/s/g;

    sget-object v1, Lm/a/c0$a;->a:Lm/a/c0$a;

    invoke-interface {p2, p1, v1}, Ll/s/g;->fold(Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;

    move-result-object p1

    iput-object p1, v0, Ll/v/d/q;->a:Ljava/lang/Object;

    :cond_1
    iget-object p1, v0, Ll/v/d/q;->a:Ljava/lang/Object;

    check-cast p1, Ll/s/g;

    invoke-interface {p0, p1}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p0

    return-object p0
.end method

.method public static final b(Ll/s/g;)Ljava/lang/String;
    .locals 2

    invoke-static {}, Lm/a/n0;->c()Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    :cond_0
    sget-object v0, Lm/a/h0;->b:Lm/a/h0$a;

    invoke-interface {p0, v0}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object v0

    check-cast v0, Lm/a/h0;

    if-nez v0, :cond_1

    return-object v1

    :cond_1
    sget-object v1, Lm/a/i0;->b:Lm/a/i0$a;

    invoke-interface {p0, v1}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object p0

    check-cast p0, Lm/a/i0;

    const-string v1, "coroutine"

    if-nez p0, :cond_2

    goto :goto_0

    :cond_2
    invoke-virtual {p0}, Lm/a/i0;->S()Ljava/lang/String;

    move-result-object p0

    if-nez p0, :cond_3

    goto :goto_0

    :cond_3
    move-object v1, p0

    :goto_0
    new-instance p0, Ljava/lang/StringBuilder;

    invoke-direct {p0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x23

    invoke-virtual {p0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Lm/a/h0;->S()J

    move-result-wide v0

    invoke-virtual {p0, v0, v1}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method private static final c(Ll/s/g;)Z
    .locals 2

    sget-object v0, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    sget-object v1, Lm/a/c0$c;->a:Lm/a/c0$c;

    invoke-interface {p0, v0, v1}, Ll/s/g;->fold(Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;

    move-result-object p0

    check-cast p0, Ljava/lang/Boolean;

    invoke-virtual {p0}, Ljava/lang/Boolean;->booleanValue()Z

    move-result p0

    return p0
.end method

.method public static final d(Lm/a/j0;Ll/s/g;)Ll/s/g;
    .locals 2

    invoke-interface {p0}, Lm/a/j0;->g()Ll/s/g;

    move-result-object p0

    const/4 v0, 0x1

    invoke-static {p0, p1, v0}, Lm/a/c0;->a(Ll/s/g;Ll/s/g;Z)Ll/s/g;

    move-result-object p0

    invoke-static {}, Lm/a/n0;->c()Z

    move-result p1

    if-eqz p1, :cond_0

    new-instance p1, Lm/a/h0;

    invoke-static {}, Lm/a/n0;->b()Ljava/util/concurrent/atomic/AtomicLong;

    move-result-object v0

    invoke-virtual {v0}, Ljava/util/concurrent/atomic/AtomicLong;->incrementAndGet()J

    move-result-wide v0

    invoke-direct {p1, v0, v1}, Lm/a/h0;-><init>(J)V

    invoke-interface {p0, p1}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p1

    goto :goto_0

    :cond_0
    move-object p1, p0

    :goto_0
    invoke-static {}, Lm/a/x0;->a()Lm/a/d0;

    move-result-object v0

    if-eq p0, v0, :cond_1

    sget-object v0, Ll/s/e;->F:Ll/s/e$b;

    invoke-interface {p0, v0}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object p0

    if-nez p0, :cond_1

    invoke-static {}, Lm/a/x0;->a()Lm/a/d0;

    move-result-object p0

    invoke-interface {p1, p0}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p1

    :cond_1
    return-object p1
.end method

.method public static final e(Ll/s/j/a/e;)Lm/a/k2;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/j/a/e;",
            ")",
            "Lm/a/k2<",
            "*>;"
        }
    .end annotation

    :cond_0
    instance-of v0, p0, Lm/a/t0;

    const/4 v1, 0x0

    if-eqz v0, :cond_1

    return-object v1

    :cond_1
    invoke-interface {p0}, Ll/s/j/a/e;->getCallerFrame()Ll/s/j/a/e;

    move-result-object p0

    if-nez p0, :cond_2

    return-object v1

    :cond_2
    instance-of v0, p0, Lm/a/k2;

    if-eqz v0, :cond_0

    check-cast p0, Lm/a/k2;

    return-object p0
.end method

.method public static final f(Ll/s/d;Ll/s/g;Ljava/lang/Object;)Lm/a/k2;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/d<",
            "*>;",
            "Ll/s/g;",
            "Ljava/lang/Object;",
            ")",
            "Lm/a/k2<",
            "*>;"
        }
    .end annotation

    instance-of v0, p0, Ll/s/j/a/e;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return-object v1

    :cond_0
    sget-object v0, Lm/a/l2;->a:Lm/a/l2;

    invoke-interface {p1, v0}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object v0

    if-eqz v0, :cond_1

    const/4 v0, 0x1

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    :goto_0
    if-nez v0, :cond_2

    return-object v1

    :cond_2
    check-cast p0, Ll/s/j/a/e;

    invoke-static {p0}, Lm/a/c0;->e(Ll/s/j/a/e;)Lm/a/k2;

    move-result-object p0

    if-nez p0, :cond_3

    goto :goto_1

    :cond_3
    invoke-virtual {p0, p1, p2}, Lm/a/k2;->H0(Ll/s/g;Ljava/lang/Object;)V

    :goto_1
    return-object p0
.end method
