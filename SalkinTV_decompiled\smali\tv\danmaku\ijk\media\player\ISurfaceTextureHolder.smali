.class public interface abstract Ltv/danmaku/ijk/media/player/ISurfaceTextureHolder;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract getSurfaceTexture()Landroid/graphics/SurfaceTexture;
.end method

.method public abstract setSurfaceTexture(Landroid/graphics/SurfaceTexture;)V
.end method

.method public abstract setSurfaceTextureHost(Ltv/danmaku/ijk/media/player/ISurfaceTextureHost;)V
.end method
