.class public final Lm/a/n;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public static final a(Ll/s/d;)Lm/a/l;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/s/d<",
            "-TT;>;)",
            "Lm/a/l<",
            "TT;>;"
        }
    .end annotation

    instance-of v0, p0, Lm/a/p2/i;

    if-nez v0, :cond_0

    new-instance v0, Lm/a/l;

    const/4 v1, 0x1

    invoke-direct {v0, p0, v1}, Lm/a/l;-><init>(Ll/s/d;I)V

    return-object v0

    :cond_0
    move-object v0, p0

    check-cast v0, Lm/a/p2/i;

    invoke-virtual {v0}, Lm/a/p2/i;->k()Lm/a/l;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_2

    :cond_1
    move-object v0, v1

    goto :goto_0

    :cond_2
    invoke-virtual {v0}, Lm/a/l;->G()Z

    move-result v2

    if-eqz v2, :cond_1

    :goto_0
    if-nez v0, :cond_3

    new-instance v0, Lm/a/l;

    const/4 v1, 0x2

    invoke-direct {v0, p0, v1}, Lm/a/l;-><init>(Ll/s/d;I)V

    :cond_3
    return-object v0
.end method

.method public static final b(Lm/a/k;Lm/a/p2/s;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lm/a/k<",
            "*>;",
            "Lm/a/p2/s;",
            ")V"
        }
    .end annotation

    new-instance v0, Lm/a/f2;

    invoke-direct {v0, p1}, Lm/a/f2;-><init>(Lm/a/p2/s;)V

    invoke-interface {p0, v0}, Lm/a/k;->n(Ll/v/c/l;)V

    return-void
.end method
