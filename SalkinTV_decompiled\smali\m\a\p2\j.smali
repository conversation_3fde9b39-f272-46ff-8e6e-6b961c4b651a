.class public final Lm/a/p2/j;
.super Ljava/lang/Object;
.source ""


# static fields
.field private static final a:Lm/a/p2/f0;

.field public static final b:Lm/a/p2/f0;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lm/a/p2/f0;

    const-string v1, "UNDEFINED"

    invoke-direct {v0, v1}, Lm/a/p2/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lm/a/p2/j;->a:Lm/a/p2/f0;

    new-instance v0, Lm/a/p2/f0;

    const-string v1, "REUSABLE_CLAIMED"

    invoke-direct {v0, v1}, Lm/a/p2/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lm/a/p2/j;->b:Lm/a/p2/f0;

    return-void
.end method

.method public static final synthetic a()Lm/a/p2/f0;
    .locals 1

    sget-object v0, Lm/a/p2/j;->a:Lm/a/p2/f0;

    return-object v0
.end method

.method public static final b(Ll/s/d;Ljava/lang/Object;Ll/v/c/l;)V
    .locals 6
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/s/d<",
            "-TT;>;",
            "Ljava/lang/Object;",
            "Ll/v/c/l<",
            "-",
            "Ljava/lang/Throwable;",
            "Ll/p;",
            ">;)V"
        }
    .end annotation

    instance-of v0, p0, Lm/a/p2/i;

    if-eqz v0, :cond_8

    check-cast p0, Lm/a/p2/i;

    invoke-static {p1, p2}, Lm/a/z;->b(Ljava/lang/Object;Ll/v/c/l;)Ljava/lang/Object;

    move-result-object p2

    iget-object v0, p0, Lm/a/p2/i;->d:Lm/a/d0;

    invoke-virtual {p0}, Lm/a/p2/i;->getContext()Ll/s/g;

    move-result-object v1

    invoke-virtual {v0, v1}, Lm/a/d0;->T(Ll/s/g;)Z

    move-result v0

    const/4 v1, 0x1

    if-eqz v0, :cond_0

    iput-object p2, p0, Lm/a/p2/i;->f:Ljava/lang/Object;

    iput v1, p0, Lm/a/u0;->c:I

    iget-object p1, p0, Lm/a/p2/i;->d:Lm/a/d0;

    invoke-virtual {p0}, Lm/a/p2/i;->getContext()Ll/s/g;

    move-result-object p2

    invoke-virtual {p1, p2, p0}, Lm/a/d0;->S(Ll/s/g;Ljava/lang/Runnable;)V

    goto/16 :goto_4

    :cond_0
    invoke-static {}, Lm/a/n0;->a()Z

    move-result v0

    sget-object v0, Lm/a/i2;->a:Lm/a/i2;

    invoke-virtual {v0}, Lm/a/i2;->a()Lm/a/a1;

    move-result-object v0

    invoke-virtual {v0}, Lm/a/a1;->b0()Z

    move-result v2

    if-eqz v2, :cond_1

    iput-object p2, p0, Lm/a/p2/i;->f:Ljava/lang/Object;

    iput v1, p0, Lm/a/u0;->c:I

    invoke-virtual {v0, p0}, Lm/a/a1;->X(Lm/a/u0;)V

    goto/16 :goto_4

    :cond_1
    invoke-virtual {v0, v1}, Lm/a/a1;->Z(Z)V

    const/4 v2, 0x0

    :try_start_0
    invoke-virtual {p0}, Lm/a/p2/i;->getContext()Ll/s/g;

    move-result-object v3

    sget-object v4, Lm/a/q1;->H:Lm/a/q1$b;

    invoke-interface {v3, v4}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object v3

    check-cast v3, Lm/a/q1;

    if-eqz v3, :cond_2

    invoke-interface {v3}, Lm/a/q1;->b()Z

    move-result v4

    if-nez v4, :cond_2

    invoke-interface {v3}, Lm/a/q1;->D()Ljava/util/concurrent/CancellationException;

    move-result-object v3

    invoke-virtual {p0, p2, v3}, Lm/a/p2/i;->a(Ljava/lang/Object;Ljava/lang/Throwable;)V

    sget-object p2, Ll/j;->a:Ll/j$a;

    invoke-static {v3}, Ll/k;->a(Ljava/lang/Throwable;)Ljava/lang/Object;

    move-result-object p2

    invoke-static {p2}, Ll/j;->a(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-interface {p0, p2}, Ll/s/d;->resumeWith(Ljava/lang/Object;)V

    const/4 p2, 0x1

    goto :goto_0

    :cond_2
    const/4 p2, 0x0

    :goto_0
    if-nez p2, :cond_7

    iget-object p2, p0, Lm/a/p2/i;->e:Ll/s/d;

    iget-object v3, p0, Lm/a/p2/i;->g:Ljava/lang/Object;

    invoke-interface {p2}, Ll/s/d;->getContext()Ll/s/g;

    move-result-object v4

    invoke-static {v4, v3}, Lm/a/p2/j0;->c(Ll/s/g;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    sget-object v5, Lm/a/p2/j0;->a:Lm/a/p2/f0;

    if-eq v3, v5, :cond_3

    invoke-static {p2, v4, v3}, Lm/a/c0;->f(Ll/s/d;Ll/s/g;Ljava/lang/Object;)Lm/a/k2;

    move-result-object p2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    goto :goto_1

    :cond_3
    move-object p2, v2

    :goto_1
    :try_start_1
    iget-object v5, p0, Lm/a/p2/i;->e:Ll/s/d;

    invoke-interface {v5, p1}, Ll/s/d;->resumeWith(Ljava/lang/Object;)V

    sget-object p1, Ll/p;->a:Ll/p;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-eqz p2, :cond_4

    :try_start_2
    invoke-virtual {p2}, Lm/a/k2;->G0()Z

    move-result p1

    if-eqz p1, :cond_7

    :cond_4
    invoke-static {v4, v3}, Lm/a/p2/j0;->a(Ll/s/g;Ljava/lang/Object;)V

    goto :goto_2

    :catchall_0
    move-exception p1

    if-eqz p2, :cond_5

    invoke-virtual {p2}, Lm/a/k2;->G0()Z

    move-result p2

    if-eqz p2, :cond_6

    :cond_5
    invoke-static {v4, v3}, Lm/a/p2/j0;->a(Ll/s/g;Ljava/lang/Object;)V

    :cond_6
    throw p1

    :cond_7
    :goto_2
    invoke-virtual {v0}, Lm/a/a1;->d0()Z

    move-result p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    if-nez p1, :cond_7

    goto :goto_3

    :catchall_1
    move-exception p1

    :try_start_3
    invoke-virtual {p0, p1, v2}, Lm/a/u0;->g(Ljava/lang/Throwable;Ljava/lang/Throwable;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    :goto_3
    invoke-virtual {v0, v1}, Lm/a/a1;->V(Z)V

    goto :goto_4

    :catchall_2
    move-exception p0

    invoke-virtual {v0, v1}, Lm/a/a1;->V(Z)V

    throw p0

    :cond_8
    invoke-interface {p0, p1}, Ll/s/d;->resumeWith(Ljava/lang/Object;)V

    :goto_4
    return-void
.end method

.method public static synthetic c(Ll/s/d;Ljava/lang/Object;Ll/v/c/l;ILjava/lang/Object;)V
    .locals 0

    and-int/lit8 p3, p3, 0x2

    if-eqz p3, :cond_0

    const/4 p2, 0x0

    :cond_0
    invoke-static {p0, p1, p2}, Lm/a/p2/j;->b(Ll/s/d;Ljava/lang/Object;Ll/v/c/l;)V

    return-void
.end method
