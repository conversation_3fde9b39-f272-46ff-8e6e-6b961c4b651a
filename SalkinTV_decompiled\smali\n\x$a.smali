.class final Ln/x$a;
.super Ln/e0/b;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ln/x;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x10
    name = "a"
.end annotation


# instance fields
.field private final b:Ln/f;

.field final synthetic c:Ln/x;


# direct methods
.method constructor <init>(Ln/x;Ln/f;)V
    .locals 2

    iput-object p1, p0, Ln/x$a;->c:Ln/x;

    const/4 v0, 0x1

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p1}, Ln/x;->g()Ljava/lang/String;

    move-result-object p1

    const/4 v1, 0x0

    aput-object p1, v0, v1

    const-string p1, "OkHttp %s"

    invoke-direct {p0, p1, v0}, Ln/e0/b;-><init>(Ljava/lang/String;[Ljava/lang/Object;)V

    iput-object p2, p0, Ln/x$a;->b:Ln/f;

    return-void
.end method


# virtual methods
.method protected k()V
    .locals 5

    const/4 v0, 0x1

    const/4 v1, 0x0

    :try_start_0
    iget-object v2, p0, Ln/x$a;->c:Ln/x;

    invoke-virtual {v2}, Ln/x;->d()Ln/a0;

    move-result-object v2

    iget-object v3, p0, Ln/x$a;->c:Ln/x;

    iget-object v3, v3, Ln/x;->b:Ln/e0/g/j;

    invoke-virtual {v3}, Ln/e0/g/j;->c()Z

    move-result v1
    :try_end_0
    .catch Ljava/io/IOException; {:try_start_0 .. :try_end_0} :catch_1
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-eqz v1, :cond_0

    :try_start_1
    iget-object v1, p0, Ln/x$a;->b:Ln/f;

    iget-object v2, p0, Ln/x$a;->c:Ln/x;

    new-instance v3, Ljava/io/IOException;

    const-string v4, "Canceled"

    invoke-direct {v3, v4}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    invoke-interface {v1, v2, v3}, Ln/f;->b(Ln/e;Ljava/io/IOException;)V

    goto :goto_0

    :cond_0
    iget-object v1, p0, Ln/x$a;->b:Ln/f;

    iget-object v3, p0, Ln/x$a;->c:Ln/x;

    invoke-interface {v1, v3, v2}, Ln/f;->a(Ln/e;Ln/a0;)V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :goto_0
    iget-object v0, p0, Ln/x$a;->c:Ln/x;

    iget-object v0, v0, Ln/x;->a:Ln/v;

    invoke-virtual {v0}, Ln/v;->g()Ln/n;

    move-result-object v0

    invoke-virtual {v0, p0}, Ln/n;->e(Ln/x$a;)V

    goto :goto_2

    :catch_0
    move-exception v1

    goto :goto_1

    :catchall_0
    move-exception v0

    goto :goto_3

    :catch_1
    move-exception v0

    move-object v1, v0

    const/4 v0, 0x0

    :goto_1
    if-eqz v0, :cond_1

    :try_start_2
    invoke-static {}, Ln/e0/j/f;->j()Ln/e0/j/f;

    move-result-object v0

    const/4 v2, 0x4

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Callback failure for "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v4, p0, Ln/x$a;->c:Ln/x;

    invoke-virtual {v4}, Ln/x;->h()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v0, v2, v3, v1}, Ln/e0/j/f;->p(ILjava/lang/String;Ljava/lang/Throwable;)V

    goto :goto_0

    :cond_1
    iget-object v0, p0, Ln/x$a;->c:Ln/x;

    invoke-static {v0}, Ln/x;->a(Ln/x;)Ln/p;

    move-result-object v0

    iget-object v2, p0, Ln/x$a;->c:Ln/x;

    invoke-virtual {v0, v2, v1}, Ln/p;->callFailed(Ln/e;Ljava/io/IOException;)V

    iget-object v0, p0, Ln/x$a;->b:Ln/f;

    iget-object v2, p0, Ln/x$a;->c:Ln/x;

    invoke-interface {v0, v2, v1}, Ln/f;->b(Ln/e;Ljava/io/IOException;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    goto :goto_0

    :goto_2
    return-void

    :goto_3
    iget-object v1, p0, Ln/x$a;->c:Ln/x;

    iget-object v1, v1, Ln/x;->a:Ln/v;

    invoke-virtual {v1}, Ln/v;->g()Ln/n;

    move-result-object v1

    invoke-virtual {v1, p0}, Ln/n;->e(Ln/x$a;)V

    goto :goto_5

    :goto_4
    throw v0

    :goto_5
    goto :goto_4
.end method

.method l()Ln/x;
    .locals 1

    iget-object v0, p0, Ln/x$a;->c:Ln/x;

    return-object v0
.end method

.method m()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Ln/x$a;->c:Ln/x;

    iget-object v0, v0, Ln/x;->d:Ln/y;

    invoke-virtual {v0}, Ln/y;->h()Ln/s;

    move-result-object v0

    invoke-virtual {v0}, Ln/s;->l()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
