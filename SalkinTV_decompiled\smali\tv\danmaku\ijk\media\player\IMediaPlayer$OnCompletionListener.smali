.class public interface abstract Ltv/danmaku/ijk/media/player/IMediaPlayer$OnCompletionListener;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ltv/danmaku/ijk/media/player/IMediaPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnCompletionListener"
.end annotation


# virtual methods
.method public abstract onCompletion(Ltv/danmaku/ijk/media/player/IMediaPlayer;)V
.end method
