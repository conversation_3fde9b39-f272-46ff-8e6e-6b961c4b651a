.class public final Ll/s/f;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public static final a(Ll/v/c/p;Ljava/lang/Object;Ll/s/d;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            "T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/v/c/p<",
            "-TR;-",
            "Ll/s/d<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;TR;",
            "Ll/s/d<",
            "-TT;>;)V"
        }
    .end annotation

    const-string v0, "<this>"

    invoke-static {p0, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "completion"

    invoke-static {p2, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-static {p0, p1, p2}, Ll/s/i/b;->a(Ll/v/c/p;Ljava/lang/Object;Ll/s/d;)Ll/s/d;

    move-result-object p0

    invoke-static {p0}, Ll/s/i/b;->b(Ll/s/d;)Ll/s/d;

    move-result-object p0

    sget-object p1, Ll/j;->a:Ll/j$a;

    sget-object p1, Ll/p;->a:Ll/p;

    invoke-static {p1}, Ll/j;->a(Ljava/lang/Object;)Ljava/lang/Object;

    invoke-interface {p0, p1}, Ll/s/d;->resumeWith(Ljava/lang/Object;)V

    return-void
.end method
