.class Ll/v/d/a$a;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll/v/d/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "a"
.end annotation


# static fields
.field private static final a:Ll/v/d/a$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ll/v/d/a$a;

    invoke-direct {v0}, Ll/v/d/a$a;-><init>()V

    sput-object v0, Ll/v/d/a$a;->a:Ll/v/d/a$a;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static synthetic a()Ll/v/d/a$a;
    .locals 1

    sget-object v0, Ll/v/d/a$a;->a:Ll/v/d/a$a;

    return-object v0
.end method
