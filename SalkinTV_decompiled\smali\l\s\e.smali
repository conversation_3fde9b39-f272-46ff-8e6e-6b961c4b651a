.class public interface abstract Ll/s/e;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/s/g$b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ll/s/e$b;,
        Ll/s/e$a;
    }
.end annotation


# static fields
.field public static final F:Ll/s/e$b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Ll/s/e$b;->a:Ll/s/e$b;

    sput-object v0, Ll/s/e;->F:Ll/s/e$b;

    return-void
.end method


# virtual methods
.method public abstract d(Ll/s/d;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/d<",
            "*>;)V"
        }
    .end annotation
.end method

.method public abstract j(Ll/s/d;)Ll/s/d;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/s/d<",
            "-TT;>;)",
            "Ll/s/d<",
            "TT;>;"
        }
    .end annotation
.end method
