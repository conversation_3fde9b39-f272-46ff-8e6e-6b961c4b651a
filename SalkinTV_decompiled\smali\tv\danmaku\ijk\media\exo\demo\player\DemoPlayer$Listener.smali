.class public interface abstract Ltv/danmaku/ijk/media/exo/demo/player/DemoPlayer$Listener;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ltv/danmaku/ijk/media/exo/demo/player/DemoPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "Listener"
.end annotation


# virtual methods
.method public abstract onError(Ljava/lang/Exception;)V
.end method

.method public abstract onStateChanged(ZI)V
.end method

.method public abstract onVideoSizeChanged(IIIF)V
.end method
