.class public final Ll/v/d/t;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final a:Ll/v/d/t;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ll/v/d/t;

    invoke-direct {v0}, Ll/v/d/t;-><init>()V

    sput-object v0, Ll/v/d/t;->a:Ll/v/d/t;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
