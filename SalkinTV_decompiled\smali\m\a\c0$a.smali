.class final Lm/a/c0$a;
.super Ll/v/d/k;
.source ""

# interfaces
.implements Ll/v/c/p;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm/a/c0;->a(Ll/s/g;Ll/s/g;Z)Ll/s/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ll/v/d/k;",
        "Ll/v/c/p<",
        "Ll/s/g;",
        "Ll/s/g$b;",
        "Ll/s/g;",
        ">;"
    }
.end annotation


# static fields
.field public static final a:Lm/a/c0$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/c0$a;

    invoke-direct {v0}, Lm/a/c0$a;-><init>()V

    sput-object v0, Lm/a/c0$a;->a:Lm/a/c0$a;

    return-void
.end method

.method constructor <init>()V
    .locals 1

    const/4 v0, 0x2

    invoke-direct {p0, v0}, Ll/v/d/k;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final a(Ll/s/g;Ll/s/g$b;)Ll/s/g;
    .locals 1

    instance-of v0, p2, Lm/a/a0;

    if-eqz v0, :cond_0

    check-cast p2, Lm/a/a0;

    invoke-interface {p2}, Lm/a/a0;->p()Lm/a/a0;

    move-result-object p2

    invoke-interface {p1, p2}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p1

    return-object p1

    :cond_0
    invoke-interface {p1, p2}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ll/s/g;

    check-cast p2, Ll/s/g$b;

    invoke-virtual {p0, p1, p2}, Lm/a/c0$a;->a(Ll/s/g;Ll/s/g$b;)Ll/s/g;

    move-result-object p1

    return-object p1
.end method
