.class public final Ll/s/i/c$b;
.super Ll/s/j/a/d;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ll/s/i/c;->a(Ll/v/c/p;Ljava/lang/Object;Ll/s/d;)Ll/s/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field private a:I

.field final synthetic b:Ll/v/c/p;

.field final synthetic c:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Ll/s/d;Ll/s/g;Ll/v/c/p;Ljava/lang/Object;)V
    .locals 0

    iput-object p3, p0, Ll/s/i/c$b;->b:Ll/v/c/p;

    iput-object p4, p0, Ll/s/i/c$b;->c:Ljava/lang/Object;

    const-string p3, "null cannot be cast to non-null type kotlin.coroutines.Continuation<kotlin.Any?>"

    invoke-static {p1, p3}, Ll/v/d/j;->c(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0, p1, p2}, Ll/s/j/a/d;-><init>(Ll/s/d;Ll/s/g;)V

    return-void
.end method


# virtual methods
.method protected invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 3

    iget v0, p0, Ll/s/i/c$b;->a:I

    const/4 v1, 0x2

    const/4 v2, 0x1

    if-eqz v0, :cond_1

    if-ne v0, v2, :cond_0

    iput v1, p0, Ll/s/i/c$b;->a:I

    invoke-static {p1}, Ll/k;->b(Ljava/lang/Object;)V

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "This coroutine had already completed"

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    iput v2, p0, Ll/s/i/c$b;->a:I

    invoke-static {p1}, Ll/k;->b(Ljava/lang/Object;)V

    iget-object p1, p0, Ll/s/i/c$b;->b:Ll/v/c/p;

    const-string v0, "null cannot be cast to non-null type kotlin.Function2<R of kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt.createCoroutineUnintercepted$lambda$1, kotlin.coroutines.Continuation<T of kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt.createCoroutineUnintercepted$lambda$1>, kotlin.Any?>"

    invoke-static {p1, v0}, Ll/v/d/j;->c(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p1, p0, Ll/s/i/c$b;->b:Ll/v/c/p;

    invoke-static {p1, v1}, Ll/v/d/u;->a(Ljava/lang/Object;I)Ljava/lang/Object;

    check-cast p1, Ll/v/c/p;

    iget-object v0, p0, Ll/s/i/c$b;->c:Ljava/lang/Object;

    invoke-interface {p1, v0, p0}, Ll/v/c/p;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    :goto_0
    return-object p1
.end method
