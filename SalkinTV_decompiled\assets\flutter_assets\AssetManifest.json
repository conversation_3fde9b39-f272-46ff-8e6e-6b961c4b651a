{"assets/fonts/Alpbay_Tor.ttf": ["assets/fonts/Alpbay_Tor.ttf"], "assets/fonts/UkijTuzTom.ttf": ["assets/fonts/UkijTuzTom.ttf"], "assets/fonts/iconfont.ttf": ["assets/fonts/iconfont.ttf"], "assets/fonts/salkin.ttf": ["assets/fonts/salkin.ttf"], "assets/images/.DS_Store": ["assets/images/.DS_Store"], "assets/images/all_green.png": ["assets/images/all_green.png"], "assets/images/awat.png": ["assets/images/awat.png"], "assets/images/default_avatar.png": ["assets/images/default_avatar.png"], "assets/images/logo.png": ["assets/images/logo.png"], "assets/images/mulazim_back.png": ["assets/images/mulazim_back.png"], "assets/images/qolpan.png": ["assets/images/qolpan.png"], "assets/images/red_vip_right.png": ["assets/images/red_vip_right.png"], "assets/images/star_more.png": ["assets/images/star_more.png"], "assets/images/toplam.png": ["assets/images/toplam.png"], "assets/images/update_bg.png": ["assets/images/update_bg.png"], "assets/images/user_center_vip.png": ["assets/images/user_center_vip.png"], "assets/images/vip_red_icon.png": ["assets/images/vip_red_icon.png"], "assets/images/vip_yellow.png": ["assets/images/vip_yellow.png"], "assets/images/vip_yellow_icon.png": ["assets/images/vip_yellow_icon.png"], "assets/images/wechat_login_back.png": ["assets/images/wechat_login_back.png"], "packages/cupertino_icons/assets/CupertinoIcons.ttf": ["packages/cupertino_icons/assets/CupertinoIcons.ttf"], "packages/fluttertoast/assets/toastify.css": ["packages/fluttertoast/assets/toastify.css"], "packages/fluttertoast/assets/toastify.js": ["packages/fluttertoast/assets/toastify.js"], "packages/wakelock_web/assets/no_sleep.js": ["packages/wakelock_web/assets/no_sleep.js"], "packages/window_manager/images/ic_chrome_close.png": ["packages/window_manager/images/ic_chrome_close.png"], "packages/window_manager/images/ic_chrome_maximize.png": ["packages/window_manager/images/ic_chrome_maximize.png"], "packages/window_manager/images/ic_chrome_minimize.png": ["packages/window_manager/images/ic_chrome_minimize.png"], "packages/window_manager/images/ic_chrome_unmaximize.png": ["packages/window_manager/images/ic_chrome_unmaximize.png"]}