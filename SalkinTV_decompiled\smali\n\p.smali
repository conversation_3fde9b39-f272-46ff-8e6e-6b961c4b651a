.class public abstract Ln/p;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ln/p$c;
    }
.end annotation


# static fields
.field public static final NONE:Ln/p;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Ln/p$a;

    invoke-direct {v0}, Ln/p$a;-><init>()V

    sput-object v0, Ln/p;->NONE:Ln/p;

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method static factory(Ln/p;)Ln/p$c;
    .locals 1

    new-instance v0, Ln/p$b;

    invoke-direct {v0, p0}, Ln/p$b;-><init>(Ln/p;)V

    return-object v0
.end method


# virtual methods
.method public callEnd(Ln/e;)V
    .locals 0

    return-void
.end method

.method public callFailed(Ln/e;Ljava/io/IOException;)V
    .locals 0

    return-void
.end method

.method public callStart(Ln/e;)V
    .locals 0

    return-void
.end method

.method public connectEnd(Ln/e;Ljava/net/InetSocketAddress;Ljava/net/Proxy;Ln/w;)V
    .locals 0
    .param p4    # Ln/w;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param

    return-void
.end method

.method public connectFailed(Ln/e;Ljava/net/InetSocketAddress;Ljava/net/Proxy;Ln/w;Ljava/io/IOException;)V
    .locals 0
    .param p4    # Ln/w;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param

    return-void
.end method

.method public connectStart(Ln/e;Ljava/net/InetSocketAddress;Ljava/net/Proxy;)V
    .locals 0

    return-void
.end method

.method public connectionAcquired(Ln/e;Ln/i;)V
    .locals 0

    return-void
.end method

.method public connectionReleased(Ln/e;Ln/i;)V
    .locals 0

    return-void
.end method

.method public dnsEnd(Ln/e;Ljava/lang/String;Ljava/util/List;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ln/e;",
            "Ljava/lang/String;",
            "Ljava/util/List<",
            "Ljava/net/InetAddress;",
            ">;)V"
        }
    .end annotation

    return-void
.end method

.method public dnsStart(Ln/e;Ljava/lang/String;)V
    .locals 0

    return-void
.end method

.method public requestBodyEnd(Ln/e;J)V
    .locals 0

    return-void
.end method

.method public requestBodyStart(Ln/e;)V
    .locals 0

    return-void
.end method

.method public requestHeadersEnd(Ln/e;Ln/y;)V
    .locals 0

    return-void
.end method

.method public requestHeadersStart(Ln/e;)V
    .locals 0

    return-void
.end method

.method public responseBodyEnd(Ln/e;J)V
    .locals 0

    return-void
.end method

.method public responseBodyStart(Ln/e;)V
    .locals 0

    return-void
.end method

.method public responseHeadersEnd(Ln/e;Ln/a0;)V
    .locals 0

    return-void
.end method

.method public responseHeadersStart(Ln/e;)V
    .locals 0

    return-void
.end method

.method public secureConnectEnd(Ln/e;Ln/q;)V
    .locals 0
    .param p2    # Ln/q;
        .annotation runtime Ljavax/annotation/Nullable;
        .end annotation
    .end param

    return-void
.end method

.method public secureConnectStart(Ln/e;)V
    .locals 0

    return-void
.end method
