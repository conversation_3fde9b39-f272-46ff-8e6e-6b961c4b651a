.class public interface abstract Ll/s/g;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ll/s/g$c;,
        Ll/s/g$b;,
        Ll/s/g$a;
    }
.end annotation


# virtual methods
.method public abstract fold(Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            ">(TR;",
            "Ll/v/c/p<",
            "-TR;-",
            "Ll/s/g$b;",
            "+TR;>;)TR;"
        }
    .end annotation
.end method

.method public abstract get(Ll/s/g$c;)Ll/s/g$b;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E::",
            "Ll/s/g$b;",
            ">(",
            "Ll/s/g$c<",
            "TE;>;)TE;"
        }
    .end annotation
.end method

.method public abstract minusKey(Ll/s/g$c;)Ll/s/g;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g$c<",
            "*>;)",
            "Ll/s/g;"
        }
    .end annotation
.end method

.method public abstract plus(Ll/s/g;)Ll/s/g;
.end method
