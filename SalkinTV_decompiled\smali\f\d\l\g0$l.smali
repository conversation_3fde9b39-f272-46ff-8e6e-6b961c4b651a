.class Lf/d/l/g0$l;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf/d/l/g0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "l"
.end annotation


# static fields
.field static final b:Lf/d/l/g0;


# instance fields
.field final a:Lf/d/l/g0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lf/d/l/g0$b;

    invoke-direct {v0}, Lf/d/l/g0$b;-><init>()V

    invoke-virtual {v0}, Lf/d/l/g0$b;->a()Lf/d/l/g0;

    move-result-object v0

    invoke-virtual {v0}, Lf/d/l/g0;->a()Lf/d/l/g0;

    move-result-object v0

    invoke-virtual {v0}, Lf/d/l/g0;->b()Lf/d/l/g0;

    move-result-object v0

    invoke-virtual {v0}, Lf/d/l/g0;->c()Lf/d/l/g0;

    move-result-object v0

    sput-object v0, Lf/d/l/g0$l;->b:Lf/d/l/g0;

    return-void
.end method

.method constructor <init>(Lf/d/l/g0;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lf/d/l/g0$l;->a:Lf/d/l/g0;

    return-void
.end method


# virtual methods
.method a()Lf/d/l/g0;
    .locals 1

    iget-object v0, p0, Lf/d/l/g0$l;->a:Lf/d/l/g0;

    return-object v0
.end method

.method b()Lf/d/l/g0;
    .locals 1

    iget-object v0, p0, Lf/d/l/g0$l;->a:Lf/d/l/g0;

    return-object v0
.end method

.method c()Lf/d/l/g0;
    .locals 1

    iget-object v0, p0, Lf/d/l/g0$l;->a:Lf/d/l/g0;

    return-object v0
.end method

.method d(Landroid/view/View;)V
    .locals 0

    return-void
.end method

.method e(Lf/d/l/g0;)V
    .locals 0

    return-void
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 4

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lf/d/l/g0$l;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lf/d/l/g0$l;

    invoke-virtual {p0}, Lf/d/l/g0$l;->o()Z

    move-result v1

    invoke-virtual {p1}, Lf/d/l/g0$l;->o()Z

    move-result v3

    if-ne v1, v3, :cond_2

    invoke-virtual {p0}, Lf/d/l/g0$l;->n()Z

    move-result v1

    invoke-virtual {p1}, Lf/d/l/g0$l;->n()Z

    move-result v3

    if-ne v1, v3, :cond_2

    invoke-virtual {p0}, Lf/d/l/g0$l;->k()Lf/d/e/b;

    move-result-object v1

    invoke-virtual {p1}, Lf/d/l/g0$l;->k()Lf/d/e/b;

    move-result-object v3

    invoke-static {v1, v3}, Lf/d/k/c;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-virtual {p0}, Lf/d/l/g0$l;->i()Lf/d/e/b;

    move-result-object v1

    invoke-virtual {p1}, Lf/d/l/g0$l;->i()Lf/d/e/b;

    move-result-object v3

    invoke-static {v1, v3}, Lf/d/k/c;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-virtual {p0}, Lf/d/l/g0$l;->f()Lf/d/l/f;

    move-result-object v1

    invoke-virtual {p1}, Lf/d/l/g0$l;->f()Lf/d/l/f;

    move-result-object p1

    invoke-static {v1, p1}, Lf/d/k/c;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    goto :goto_0

    :cond_2
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method f()Lf/d/l/f;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method g(I)Lf/d/e/b;
    .locals 0

    sget-object p1, Lf/d/e/b;->e:Lf/d/e/b;

    return-object p1
.end method

.method h()Lf/d/e/b;
    .locals 1

    invoke-virtual {p0}, Lf/d/l/g0$l;->k()Lf/d/e/b;

    move-result-object v0

    return-object v0
.end method

.method public hashCode()I
    .locals 3

    const/4 v0, 0x5

    new-array v0, v0, [Ljava/lang/Object;

    invoke-virtual {p0}, Lf/d/l/g0$l;->o()Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    invoke-virtual {p0}, Lf/d/l/g0$l;->n()Z

    move-result v1

    invoke-static {v1}, Ljava/lang/Boolean;->valueOf(Z)Ljava/lang/Boolean;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    invoke-virtual {p0}, Lf/d/l/g0$l;->k()Lf/d/e/b;

    move-result-object v1

    const/4 v2, 0x2

    aput-object v1, v0, v2

    invoke-virtual {p0}, Lf/d/l/g0$l;->i()Lf/d/e/b;

    move-result-object v1

    const/4 v2, 0x3

    aput-object v1, v0, v2

    invoke-virtual {p0}, Lf/d/l/g0$l;->f()Lf/d/l/f;

    move-result-object v1

    const/4 v2, 0x4

    aput-object v1, v0, v2

    invoke-static {v0}, Lf/d/k/c;->b([Ljava/lang/Object;)I

    move-result v0

    return v0
.end method

.method i()Lf/d/e/b;
    .locals 1

    sget-object v0, Lf/d/e/b;->e:Lf/d/e/b;

    return-object v0
.end method

.method j()Lf/d/e/b;
    .locals 1

    invoke-virtual {p0}, Lf/d/l/g0$l;->k()Lf/d/e/b;

    move-result-object v0

    return-object v0
.end method

.method k()Lf/d/e/b;
    .locals 1

    sget-object v0, Lf/d/e/b;->e:Lf/d/e/b;

    return-object v0
.end method

.method l()Lf/d/e/b;
    .locals 1

    invoke-virtual {p0}, Lf/d/l/g0$l;->k()Lf/d/e/b;

    move-result-object v0

    return-object v0
.end method

.method m(IIII)Lf/d/l/g0;
    .locals 0

    sget-object p1, Lf/d/l/g0$l;->b:Lf/d/l/g0;

    return-object p1
.end method

.method n()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method o()Z
    .locals 1

    const/4 v0, 0x0

    return v0
.end method

.method public p([Lf/d/e/b;)V
    .locals 0

    return-void
.end method

.method q(Lf/d/e/b;)V
    .locals 0

    return-void
.end method

.method r(Lf/d/l/g0;)V
    .locals 0

    return-void
.end method

.method public s(Lf/d/e/b;)V
    .locals 0

    return-void
.end method
