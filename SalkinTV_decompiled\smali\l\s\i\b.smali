.class public final Ll/s/i/b;
.super Ll/s/i/d;
.source ""


# direct methods
.method public static bridge synthetic a(Ll/v/c/p;Ljava/lang/Object;Ll/s/d;)Ll/s/d;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            "T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/v/c/p<",
            "-TR;-",
            "Ll/s/d<",
            "-TT;>;+",
            "Ljava/lang/Object;",
            ">;TR;",
            "Ll/s/d<",
            "-TT;>;)",
            "Ll/s/d<",
            "Ll/p;",
            ">;"
        }
    .end annotation

    invoke-static {p0, p1, p2}, Ll/s/i/c;->a(Ll/v/c/p;Ljava/lang/Object;Ll/s/d;)Ll/s/d;

    move-result-object p0

    return-object p0
.end method

.method public static bridge synthetic b(Ll/s/d;)Ll/s/d;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/s/d<",
            "-TT;>;)",
            "Ll/s/d<",
            "TT;>;"
        }
    .end annotation

    invoke-static {p0}, Ll/s/i/c;->b(Ll/s/d;)Ll/s/d;

    move-result-object p0

    return-object p0
.end method

.method public static bridge synthetic c()Ljava/lang/Object;
    .locals 1

    invoke-static {}, Ll/s/i/d;->c()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
