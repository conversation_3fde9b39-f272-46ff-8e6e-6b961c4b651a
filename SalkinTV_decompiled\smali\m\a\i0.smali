.class public final Lm/a/i0;
.super Ll/s/a;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lm/a/i0$a;
    }
.end annotation


# static fields
.field public static final b:Lm/a/i0$a;


# instance fields
.field private final a:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lm/a/i0$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lm/a/i0$a;-><init>(Ll/v/d/e;)V

    sput-object v0, Lm/a/i0;->b:Lm/a/i0$a;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 1

    sget-object v0, Lm/a/i0;->b:Lm/a/i0$a;

    invoke-direct {p0, v0}, Ll/s/a;-><init>(Ll/s/g$c;)V

    iput-object p1, p0, Lm/a/i0;->a:Ljava/lang/String;

    return-void
.end method


# virtual methods
.method public final S()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Lm/a/i0;->a:Ljava/lang/String;

    return-object v0
.end method

.method public equals(Ljava/lang/Object;)Z
    .locals 3

    const/4 v0, 0x1

    if-ne p0, p1, :cond_0

    return v0

    :cond_0
    instance-of v1, p1, Lm/a/i0;

    const/4 v2, 0x0

    if-nez v1, :cond_1

    return v2

    :cond_1
    check-cast p1, Lm/a/i0;

    iget-object v1, p0, Lm/a/i0;->a:Ljava/lang/String;

    iget-object p1, p1, Lm/a/i0;->a:Ljava/lang/String;

    invoke-static {v1, p1}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_2

    return v2

    :cond_2
    return v0
.end method

.method public hashCode()I
    .locals 1

    iget-object v0, p0, Lm/a/i0;->a:Ljava/lang/String;

    invoke-virtual {v0}, Ljava/lang/String;->hashCode()I

    move-result v0

    return v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "CoroutineName("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lm/a/i0;->a:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x29

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
