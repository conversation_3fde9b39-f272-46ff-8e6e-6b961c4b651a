.class public interface abstract Ll/s/d;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract getContext()Ll/s/g;
.end method

.method public abstract resumeWith(Ljava/lang/Object;)V
.end method
