.class public final Lm/a/p2/j0;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final a:Lm/a/p2/f0;

.field private static final b:Ll/v/c/p;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/v/c/p<",
            "Ljava/lang/Object;",
            "Ll/s/g$b;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private static final c:Ll/v/c/p;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/v/c/p<",
            "Lm/a/h2<",
            "*>;",
            "Ll/s/g$b;",
            "Lm/a/h2<",
            "*>;>;"
        }
    .end annotation
.end field

.field private static final d:Ll/v/c/p;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/v/c/p<",
            "Lm/a/p2/m0;",
            "Ll/s/g$b;",
            "Lm/a/p2/m0;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lm/a/p2/f0;

    const-string v1, "NO_THREAD_ELEMENTS"

    invoke-direct {v0, v1}, Lm/a/p2/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lm/a/p2/j0;->a:Lm/a/p2/f0;

    sget-object v0, Lm/a/p2/j0$a;->a:Lm/a/p2/j0$a;

    sput-object v0, Lm/a/p2/j0;->b:Ll/v/c/p;

    sget-object v0, Lm/a/p2/j0$b;->a:Lm/a/p2/j0$b;

    sput-object v0, Lm/a/p2/j0;->c:Ll/v/c/p;

    sget-object v0, Lm/a/p2/j0$c;->a:Lm/a/p2/j0$c;

    sput-object v0, Lm/a/p2/j0;->d:Ll/v/c/p;

    return-void
.end method

.method public static final a(Ll/s/g;Ljava/lang/Object;)V
    .locals 2

    sget-object v0, Lm/a/p2/j0;->a:Lm/a/p2/f0;

    if-ne p1, v0, :cond_0

    return-void

    :cond_0
    instance-of v0, p1, Lm/a/p2/m0;

    if-eqz v0, :cond_1

    check-cast p1, Lm/a/p2/m0;

    invoke-virtual {p1, p0}, Lm/a/p2/m0;->b(Ll/s/g;)V

    goto :goto_0

    :cond_1
    const/4 v0, 0x0

    sget-object v1, Lm/a/p2/j0;->c:Ll/v/c/p;

    invoke-interface {p0, v0, v1}, Ll/s/g;->fold(Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_2

    check-cast v0, Lm/a/h2;

    invoke-interface {v0, p0, p1}, Lm/a/h2;->r(Ll/s/g;Ljava/lang/Object;)V

    :goto_0
    return-void

    :cond_2
    new-instance p0, Ljava/lang/NullPointerException;

    const-string p1, "null cannot be cast to non-null type kotlinx.coroutines.ThreadContextElement<kotlin.Any?>"

    invoke-direct {p0, p1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p0
.end method

.method public static final b(Ll/s/g;)Ljava/lang/Object;
    .locals 2

    const/4 v0, 0x0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    sget-object v1, Lm/a/p2/j0;->b:Ll/v/c/p;

    invoke-interface {p0, v0, v1}, Ll/s/g;->fold(Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;

    move-result-object p0

    invoke-static {p0}, Ll/v/d/j;->b(Ljava/lang/Object;)V

    return-object p0
.end method

.method public static final c(Ll/s/g;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    if-nez p1, :cond_0

    invoke-static {p0}, Lm/a/p2/j0;->b(Ll/s/g;)Ljava/lang/Object;

    move-result-object p1

    :cond_0
    const/4 v0, 0x0

    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    move-result-object v0

    if-ne p1, v0, :cond_1

    sget-object p0, Lm/a/p2/j0;->a:Lm/a/p2/f0;

    goto :goto_0

    :cond_1
    instance-of v0, p1, Ljava/lang/Integer;

    if-eqz v0, :cond_2

    new-instance v0, Lm/a/p2/m0;

    check-cast p1, Ljava/lang/Number;

    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    move-result p1

    invoke-direct {v0, p0, p1}, Lm/a/p2/m0;-><init>(Ll/s/g;I)V

    sget-object p1, Lm/a/p2/j0;->d:Ll/v/c/p;

    invoke-interface {p0, v0, p1}, Ll/s/g;->fold(Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;

    move-result-object p0

    goto :goto_0

    :cond_2
    check-cast p1, Lm/a/h2;

    invoke-interface {p1, p0}, Lm/a/h2;->K(Ll/s/g;)Ljava/lang/Object;

    move-result-object p0

    :goto_0
    return-object p0
.end method
