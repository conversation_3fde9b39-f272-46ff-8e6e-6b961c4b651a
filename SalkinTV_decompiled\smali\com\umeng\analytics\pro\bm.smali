.class public Lcom/umeng/analytics/pro/bm;
.super Lcom/umeng/analytics/pro/bj;
.source ""


# instance fields
.field public final a:Lcom/umeng/analytics/pro/bj;


# direct methods
.method public constructor <init>(BLcom/umeng/analytics/pro/bj;)V
    .locals 0

    invoke-direct {p0, p1}, Lcom/umeng/analytics/pro/bj;-><init>(B)V

    iput-object p2, p0, Lcom/umeng/analytics/pro/bm;->a:Lcom/umeng/analytics/pro/bj;

    return-void
.end method
