.class public final Lm/a/g1$a;
.super Ll/s/b;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lm/a/g1;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ll/s/b<",
        "Lm/a/d0;",
        "Lm/a/g1;",
        ">;"
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 2

    sget-object v0, Lm/a/d0;->a:Lm/a/d0$a;

    sget-object v1, Lm/a/g1$a$a;->a:Lm/a/g1$a$a;

    invoke-direct {p0, v0, v1}, Ll/s/b;-><init>(Ll/s/g$c;Ll/v/c/l;)V

    return-void
.end method

.method public synthetic constructor <init>(Ll/v/d/e;)V
    .locals 0

    invoke-direct {p0}, Lm/a/g1$a;-><init>()V

    return-void
.end method
