.class final Lo/m;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lo/d;


# instance fields
.field public final a:Lo/c;

.field public final b:Lo/r;

.field c:Z


# direct methods
.method constructor <init>(Lo/r;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Lo/c;

    invoke-direct {v0}, Lo/c;-><init>()V

    iput-object v0, p0, Lo/m;->a:Lo/c;

    if-eqz p1, :cond_0

    iput-object p1, p0, Lo/m;->b:Lo/r;

    return-void

    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v0, "sink == null"

    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method


# virtual methods
.method public C()Lo/d;
    .locals 5

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0}, Lo/c;->d()J

    move-result-wide v0

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_0

    iget-object v2, p0, Lo/m;->b:Lo/r;

    iget-object v3, p0, Lo/m;->a:Lo/c;

    invoke-interface {v2, v3, v0, v1}, Lo/r;->g(Lo/c;J)V

    :cond_0
    return-object p0

    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "closed"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public J(Ljava/lang/String;)Lo/d;
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0, p1}, Lo/c;->j0(Ljava/lang/String;)Lo/c;

    invoke-virtual {p0}, Lo/m;->C()Lo/d;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "closed"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public close()V
    .locals 7

    iget-boolean v0, p0, Lo/m;->c:Z

    if-eqz v0, :cond_0

    return-void

    :cond_0
    const/4 v0, 0x0

    :try_start_0
    iget-object v1, p0, Lo/m;->a:Lo/c;

    iget-wide v2, v1, Lo/c;->b:J

    const-wide/16 v4, 0x0

    cmp-long v6, v2, v4

    if-lez v6, :cond_1

    iget-object v4, p0, Lo/m;->b:Lo/r;

    invoke-interface {v4, v1, v2, v3}, Lo/r;->g(Lo/c;J)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    :cond_1
    move-object v1, v0

    goto :goto_0

    :catchall_0
    move-exception v1

    :goto_0
    :try_start_1
    iget-object v2, p0, Lo/m;->b:Lo/r;

    invoke-interface {v2}, Lo/r;->close()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    goto :goto_1

    :catchall_1
    move-exception v2

    if-nez v1, :cond_2

    move-object v1, v2

    :cond_2
    :goto_1
    const/4 v2, 0x1

    iput-boolean v2, p0, Lo/m;->c:Z

    if-nez v1, :cond_3

    return-void

    :cond_3
    invoke-static {v1}, Lo/u;->e(Ljava/lang/Throwable;)V

    throw v0
.end method

.method public e()Lo/c;
    .locals 1

    iget-object v0, p0, Lo/m;->a:Lo/c;

    return-object v0
.end method

.method public f()Lo/t;
    .locals 1

    iget-object v0, p0, Lo/m;->b:Lo/r;

    invoke-interface {v0}, Lo/r;->f()Lo/t;

    move-result-object v0

    return-object v0
.end method

.method public flush()V
    .locals 6

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_1

    iget-object v0, p0, Lo/m;->a:Lo/c;

    iget-wide v1, v0, Lo/c;->b:J

    const-wide/16 v3, 0x0

    cmp-long v5, v1, v3

    if-lez v5, :cond_0

    iget-object v3, p0, Lo/m;->b:Lo/r;

    invoke-interface {v3, v0, v1, v2}, Lo/r;->g(Lo/c;J)V

    :cond_0
    iget-object v0, p0, Lo/m;->b:Lo/r;

    invoke-interface {v0}, Lo/r;->flush()V

    return-void

    :cond_1
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "closed"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0
.end method

.method public g(Lo/c;J)V
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0, p1, p2, p3}, Lo/c;->g(Lo/c;J)V

    invoke-virtual {p0}, Lo/m;->C()Lo/d;

    return-void

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public h(J)Lo/d;
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0, p1, p2}, Lo/c;->e0(J)Lo/c;

    invoke-virtual {p0}, Lo/m;->C()Lo/d;

    move-result-object p1

    return-object p1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public isOpen()Z
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    xor-int/lit8 v0, v0, 0x1

    return v0
.end method

.method public l(I)Lo/d;
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0, p1}, Lo/c;->g0(I)Lo/c;

    invoke-virtual {p0}, Lo/m;->C()Lo/d;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "closed"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public o(I)Lo/d;
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0, p1}, Lo/c;->f0(I)Lo/c;

    invoke-virtual {p0}, Lo/m;->C()Lo/d;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "closed"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "buffer("

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lo/m;->b:Lo/r;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ")"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public v(I)Lo/d;
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0, p1}, Lo/c;->d0(I)Lo/c;

    invoke-virtual {p0}, Lo/m;->C()Lo/d;

    move-result-object p1

    return-object p1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "closed"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public write(Ljava/nio/ByteBuffer;)I
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0, p1}, Lo/c;->write(Ljava/nio/ByteBuffer;)I

    move-result p1

    invoke-virtual {p0}, Lo/m;->C()Lo/d;

    return p1

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "closed"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public write([BII)Lo/d;
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0, p1, p2, p3}, Lo/c;->b0([BII)Lo/c;

    invoke-virtual {p0}, Lo/m;->C()Lo/d;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "closed"

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public x([B)Lo/d;
    .locals 1

    iget-boolean v0, p0, Lo/m;->c:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Lo/m;->a:Lo/c;

    invoke-virtual {v0, p1}, Lo/c;->a0([B)Lo/c;

    invoke-virtual {p0}, Lo/m;->C()Lo/d;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "closed"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1
.end method
