.class public final Ll/s/g$a;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll/s/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method public static a(Ll/s/g;Ll/s/g;)Ll/s/g;
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    sget-object v0, Ll/s/h;->a:Ll/s/h;

    if-ne p1, v0, :cond_0

    goto :goto_0

    :cond_0
    sget-object v0, Ll/s/g$a$a;->a:Ll/s/g$a$a;

    invoke-interface {p1, p0, v0}, Ll/s/g;->fold(Ljava/lang/Object;Ll/v/c/p;)L<PERSON><PERSON>/lang/Object;

    move-result-object p0

    check-cast p0, Ll/s/g;

    :goto_0
    return-object p0
.end method
