.class public final synthetic Lk/a/d/a;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lk/a/d/h$a;


# static fields
.field public static final synthetic a:Lk/a/d/a;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lk/a/d/a;

    invoke-direct {v0}, Lk/a/d/a;-><init>()V

    sput-object v0, Lk/a/d/a;->a:Lk/a/d/a;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(Landroid/view/View;)Z
    .locals 0

    invoke-static {p1}, Lk/a/d/h;->e(Landroid/view/View;)Z

    move-result p1

    return p1
.end method
