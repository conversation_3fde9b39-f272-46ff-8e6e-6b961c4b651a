.class Ll/f;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public static a(Ll/v/c/a;)Ll/d;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/v/c/a<",
            "+TT;>;)",
            "Ll/d<",
            "TT;>;"
        }
    .end annotation

    const-string v0, "initializer"

    invoke-static {p0, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    new-instance v0, Ll/l;

    const/4 v1, 0x0

    const/4 v2, 0x2

    invoke-direct {v0, p0, v1, v2, v1}, Ll/l;-><init>(Ll/v/c/a;Ljava/lang/Object;ILl/v/d/e;)V

    return-object v0
.end method
