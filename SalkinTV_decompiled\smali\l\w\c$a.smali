.class public final Ll/w/c$a;
.super Ll/w/c;
.source ""

# interfaces
.implements Ljava/io/Serializable;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ll/w/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ll/w/c;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Ll/v/d/e;)V
    .locals 0

    invoke-direct {p0}, Ll/w/c$a;-><init>()V

    return-void
.end method


# virtual methods
.method public b()I
    .locals 1

    invoke-static {}, Ll/w/c;->a()Ll/w/c;

    move-result-object v0

    invoke-virtual {v0}, Ll/w/c;->b()I

    move-result v0

    return v0
.end method
