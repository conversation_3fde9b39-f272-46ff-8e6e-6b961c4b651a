.class public final Lm/a/g;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public static final a(Lm/a/j0;Ll/s/g;Lm/a/l0;Ll/v/c/p;)Lm/a/q1;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lm/a/j0;",
            "Ll/s/g;",
            "Lm/a/l0;",
            "Ll/v/c/p<",
            "-",
            "Lm/a/j0;",
            "-",
            "Ll/s/d<",
            "-",
            "Ll/p;",
            ">;+",
            "Ljava/lang/Object;",
            ">;)",
            "Lm/a/q1;"
        }
    .end annotation

    invoke-static {p0, p1, p2, p3}, Lm/a/h;->a(Lm/a/j0;Ll/s/g;Lm/a/l0;Ll/v/c/p;)Lm/a/q1;

    move-result-object p0

    return-object p0
.end method

.method public static synthetic b(Lm/a/j0;Ll/s/g;Lm/a/l0;Ll/v/c/p;ILjava/lang/Object;)Lm/a/q1;
    .locals 0

    invoke-static/range {p0 .. p5}, Lm/a/h;->b(Lm/a/j0;Ll/s/g;Lm/a/l0;Ll/v/c/p;ILjava/lang/Object;)Lm/a/q1;

    move-result-object p0

    return-object p0
.end method
