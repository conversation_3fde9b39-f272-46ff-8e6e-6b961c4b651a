.class final Ln/x;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ln/e;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ln/x$a;
    }
.end annotation


# instance fields
.field final a:Ln/v;

.field final b:Ln/e0/g/j;

.field private c:Ln/p;

.field final d:Ln/y;

.field final e:Z

.field private f:Z


# direct methods
.method private constructor <init>(Ln/v;Ln/y;Z)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ln/x;->a:Ln/v;

    iput-object p2, p0, Ln/x;->d:Ln/y;

    iput-boolean p3, p0, Ln/x;->e:Z

    new-instance p2, Ln/e0/g/j;

    invoke-direct {p2, p1, p3}, Ln/e0/g/j;-><init>(Ln/v;Z)V

    iput-object p2, p0, Ln/x;->b:Ln/e0/g/j;

    return-void
.end method

.method static synthetic a(Ln/x;)Ln/p;
    .locals 0

    iget-object p0, p0, Ln/x;->c:Ln/p;

    return-object p0
.end method

.method private b()V
    .locals 2

    invoke-static {}, Ln/e0/j/f;->j()Ln/e0/j/f;

    move-result-object v0

    const-string v1, "response.body().close()"

    invoke-virtual {v0, v1}, Ln/e0/j/f;->m(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    iget-object v1, p0, Ln/x;->b:Ln/e0/g/j;

    invoke-virtual {v1, v0}, Ln/e0/g/j;->h(Ljava/lang/Object;)V

    return-void
.end method

.method static f(Ln/v;Ln/y;Z)Ln/x;
    .locals 1

    new-instance v0, Ln/x;

    invoke-direct {v0, p0, p1, p2}, Ln/x;-><init>(Ln/v;Ln/y;Z)V

    invoke-virtual {p0}, Ln/v;->i()Ln/p$c;

    move-result-object p0

    invoke-interface {p0, v0}, Ln/p$c;->create(Ln/e;)Ln/p;

    move-result-object p0

    iput-object p0, v0, Ln/x;->c:Ln/p;

    return-object v0
.end method


# virtual methods
.method public S()Ln/y;
    .locals 1

    iget-object v0, p0, Ln/x;->d:Ln/y;

    return-object v0
.end method

.method public T(Ln/f;)V
    .locals 2

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Ln/x;->f:Z

    if-nez v0, :cond_0

    const/4 v0, 0x1

    iput-boolean v0, p0, Ln/x;->f:Z

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    invoke-direct {p0}, Ln/x;->b()V

    iget-object v0, p0, Ln/x;->c:Ln/p;

    invoke-virtual {v0, p0}, Ln/p;->callStart(Ln/e;)V

    iget-object v0, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v0}, Ln/v;->g()Ln/n;

    move-result-object v0

    new-instance v1, Ln/x$a;

    invoke-direct {v1, p0, p1}, Ln/x$a;-><init>(Ln/x;Ln/f;)V

    invoke-virtual {v0, v1}, Ln/n;->a(Ln/x$a;)V

    return-void

    :cond_0
    :try_start_1
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v0, "Already Executed"

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :catchall_0
    move-exception p1

    monitor-exit p0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1
.end method

.method public U()Ln/a0;
    .locals 2

    monitor-enter p0

    :try_start_0
    iget-boolean v0, p0, Ln/x;->f:Z

    if-nez v0, :cond_1

    const/4 v0, 0x1

    iput-boolean v0, p0, Ln/x;->f:Z

    monitor-exit p0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    invoke-direct {p0}, Ln/x;->b()V

    iget-object v0, p0, Ln/x;->c:Ln/p;

    invoke-virtual {v0, p0}, Ln/p;->callStart(Ln/e;)V

    :try_start_1
    iget-object v0, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v0}, Ln/v;->g()Ln/n;

    move-result-object v0

    invoke-virtual {v0, p0}, Ln/n;->b(Ln/x;)V

    invoke-virtual {p0}, Ln/x;->d()Ln/a0;

    move-result-object v0
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    if-eqz v0, :cond_0

    iget-object v1, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v1}, Ln/v;->g()Ln/n;

    move-result-object v1

    invoke-virtual {v1, p0}, Ln/n;->f(Ln/x;)V

    return-object v0

    :cond_0
    :try_start_2
    new-instance v0, Ljava/io/IOException;

    const-string v1, "Canceled"

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    :catchall_0
    move-exception v0

    goto :goto_0

    :catch_0
    move-exception v0

    :try_start_3
    iget-object v1, p0, Ln/x;->c:Ln/p;

    invoke-virtual {v1, p0, v0}, Ln/p;->callFailed(Ln/e;Ljava/io/IOException;)V

    throw v0
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    :goto_0
    iget-object v1, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v1}, Ln/v;->g()Ln/n;

    move-result-object v1

    invoke-virtual {v1, p0}, Ln/n;->f(Ln/x;)V

    throw v0

    :cond_1
    :try_start_4
    new-instance v0, Ljava/lang/IllegalStateException;

    const-string v1, "Already Executed"

    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw v0

    :catchall_1
    move-exception v0

    monitor-exit p0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    throw v0
.end method

.method public c()Ln/x;
    .locals 3

    iget-object v0, p0, Ln/x;->a:Ln/v;

    iget-object v1, p0, Ln/x;->d:Ln/y;

    iget-boolean v2, p0, Ln/x;->e:Z

    invoke-static {v0, v1, v2}, Ln/x;->f(Ln/v;Ln/y;Z)Ln/x;

    move-result-object v0

    return-object v0
.end method

.method public bridge synthetic clone()Ljava/lang/Object;
    .locals 1

    invoke-virtual {p0}, Ln/x;->c()Ln/x;

    move-result-object v0

    return-object v0
.end method

.method d()Ln/a0;
    .locals 13

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iget-object v0, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v0}, Ln/v;->m()Ljava/util/List;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    iget-object v0, p0, Ln/x;->b:Ln/e0/g/j;

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v0, Ln/e0/g/a;

    iget-object v2, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v2}, Ln/v;->f()Ln/m;

    move-result-object v2

    invoke-direct {v0, v2}, Ln/e0/g/a;-><init>(Ln/m;)V

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v0, Ln/e0/e/a;

    iget-object v2, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v2}, Ln/v;->n()Ln/e0/e/d;

    move-result-object v2

    invoke-direct {v0, v2}, Ln/e0/e/a;-><init>(Ln/e0/e/d;)V

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v0, Ln/e0/f/a;

    iget-object v2, p0, Ln/x;->a:Ln/v;

    invoke-direct {v0, v2}, Ln/e0/f/a;-><init>(Ln/v;)V

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    iget-boolean v0, p0, Ln/x;->e:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v0}, Ln/v;->o()Ljava/util/List;

    move-result-object v0

    invoke-interface {v1, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    :cond_0
    new-instance v0, Ln/e0/g/b;

    iget-boolean v2, p0, Ln/x;->e:Z

    invoke-direct {v0, v2}, Ln/e0/g/b;-><init>(Z)V

    invoke-interface {v1, v0}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    new-instance v12, Ln/e0/g/g;

    const/4 v2, 0x0

    const/4 v3, 0x0

    const/4 v4, 0x0

    const/4 v5, 0x0

    iget-object v6, p0, Ln/x;->d:Ln/y;

    iget-object v8, p0, Ln/x;->c:Ln/p;

    iget-object v0, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v0}, Ln/v;->c()I

    move-result v9

    iget-object v0, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v0}, Ln/v;->x()I

    move-result v10

    iget-object v0, p0, Ln/x;->a:Ln/v;

    invoke-virtual {v0}, Ln/v;->B()I

    move-result v11

    move-object v0, v12

    move-object v7, p0

    invoke-direct/range {v0 .. v11}, Ln/e0/g/g;-><init>(Ljava/util/List;Ln/e0/f/g;Ln/e0/g/c;Ln/e0/f/c;ILn/y;Ln/e;Ln/p;III)V

    iget-object v0, p0, Ln/x;->d:Ln/y;

    invoke-interface {v12, v0}, Ln/t$a;->d(Ln/y;)Ln/a0;

    move-result-object v0

    return-object v0
.end method

.method public e()Z
    .locals 1

    iget-object v0, p0, Ln/x;->b:Ln/e0/g/j;

    invoke-virtual {v0}, Ln/e0/g/j;->c()Z

    move-result v0

    return v0
.end method

.method g()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Ln/x;->d:Ln/y;

    invoke-virtual {v0}, Ln/y;->h()Ln/s;

    move-result-object v0

    invoke-virtual {v0}, Ln/s;->A()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method h()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {p0}, Ln/x;->e()Z

    move-result v1

    if-eqz v1, :cond_0

    const-string v1, "canceled "

    goto :goto_0

    :cond_0
    const-string v1, ""

    :goto_0
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-boolean v1, p0, Ln/x;->e:Z

    if-eqz v1, :cond_1

    const-string v1, "web socket"

    goto :goto_1

    :cond_1
    const-string v1, "call"

    :goto_1
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, " to "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Ln/x;->g()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
