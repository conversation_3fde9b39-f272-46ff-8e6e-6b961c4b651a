.class public final Lm/a/m2;
.super Ll/s/a;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lm/a/m2$a;
    }
.end annotation


# static fields
.field public static final b:Lm/a/m2$a;


# instance fields
.field public a:Z


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lm/a/m2$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lm/a/m2$a;-><init>(Ll/v/d/e;)V

    sput-object v0, Lm/a/m2;->b:Lm/a/m2$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    sget-object v0, Lm/a/m2;->b:Lm/a/m2$a;

    invoke-direct {p0, v0}, Ll/s/a;-><init>(Ll/s/g$c;)V

    return-void
.end method
