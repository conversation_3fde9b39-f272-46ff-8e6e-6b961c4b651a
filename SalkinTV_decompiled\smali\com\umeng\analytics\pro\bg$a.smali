.class Lcom/umeng/analytics/pro/bg$a;
.super Lcom/umeng/analytics/pro/cf;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/umeng/analytics/pro/bg;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/umeng/analytics/pro/cf<",
        "Lcom/umeng/analytics/pro/bg;",
        ">;"
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/umeng/analytics/pro/cf;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Lcom/umeng/analytics/pro/bg$1;)V
    .locals 0

    invoke-direct {p0}, Lcom/umeng/analytics/pro/bg$a;-><init>()V

    return-void
.end method


# virtual methods
.method public synthetic a(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/aw;)V
    .locals 0

    check-cast p2, Lcom/umeng/analytics/pro/bg;

    invoke-virtual {p0, p1, p2}, Lcom/umeng/analytics/pro/bg$a;->b(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/bg;)V

    return-void
.end method

.method public a(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/bg;)V
    .locals 2

    const/4 v0, 0x0

    iput-object v0, p2, Lcom/umeng/analytics/pro/bg;->b:Lcom/umeng/analytics/pro/bd;

    iput-object v0, p2, Lcom/umeng/analytics/pro/bg;->a:Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/umeng/analytics/pro/bv;->j()Lcom/umeng/analytics/pro/ca;

    invoke-virtual {p1}, Lcom/umeng/analytics/pro/bv;->l()Lcom/umeng/analytics/pro/bq;

    move-result-object v0

    invoke-virtual {p2, p1, v0}, Lcom/umeng/analytics/pro/bg;->a(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/bq;)Ljava/lang/Object;

    move-result-object v1

    iput-object v1, p2, Lcom/umeng/analytics/pro/bg;->a:Ljava/lang/Object;

    if-eqz v1, :cond_0

    iget-short v0, v0, Lcom/umeng/analytics/pro/bq;->c:S

    invoke-virtual {p2, v0}, Lcom/umeng/analytics/pro/bg;->a(S)Lcom/umeng/analytics/pro/bd;

    move-result-object v0

    iput-object v0, p2, Lcom/umeng/analytics/pro/bg;->b:Lcom/umeng/analytics/pro/bd;

    :cond_0
    invoke-virtual {p1}, Lcom/umeng/analytics/pro/bv;->m()V

    invoke-virtual {p1}, Lcom/umeng/analytics/pro/bv;->l()Lcom/umeng/analytics/pro/bq;

    invoke-virtual {p1}, Lcom/umeng/analytics/pro/bv;->k()V

    return-void
.end method

.method public synthetic b(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/aw;)V
    .locals 0

    check-cast p2, Lcom/umeng/analytics/pro/bg;

    invoke-virtual {p0, p1, p2}, Lcom/umeng/analytics/pro/bg$a;->a(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/bg;)V

    return-void
.end method

.method public b(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/bg;)V
    .locals 1

    invoke-virtual {p2}, Lcom/umeng/analytics/pro/bg;->a()Lcom/umeng/analytics/pro/bd;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p2}, Lcom/umeng/analytics/pro/bg;->b()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p2}, Lcom/umeng/analytics/pro/bg;->d()Lcom/umeng/analytics/pro/ca;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/umeng/analytics/pro/bv;->a(Lcom/umeng/analytics/pro/ca;)V

    iget-object v0, p2, Lcom/umeng/analytics/pro/bg;->b:Lcom/umeng/analytics/pro/bd;

    invoke-virtual {p2, v0}, Lcom/umeng/analytics/pro/bg;->c(Lcom/umeng/analytics/pro/bd;)Lcom/umeng/analytics/pro/bq;

    move-result-object v0

    invoke-virtual {p1, v0}, Lcom/umeng/analytics/pro/bv;->a(Lcom/umeng/analytics/pro/bq;)V

    invoke-virtual {p2, p1}, Lcom/umeng/analytics/pro/bg;->a(Lcom/umeng/analytics/pro/bv;)V

    invoke-virtual {p1}, Lcom/umeng/analytics/pro/bv;->c()V

    invoke-virtual {p1}, Lcom/umeng/analytics/pro/bv;->d()V

    invoke-virtual {p1}, Lcom/umeng/analytics/pro/bv;->b()V

    return-void

    :cond_0
    new-instance p1, Lcom/umeng/analytics/pro/bw;

    const-string p2, "Cannot write a TUnion with no set value!"

    invoke-direct {p1, p2}, Lcom/umeng/analytics/pro/bw;-><init>(Ljava/lang/String;)V

    throw p1
.end method
