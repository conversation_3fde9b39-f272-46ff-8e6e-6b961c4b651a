.class public final Lk/b/a/a/a/c$a;
.super Landroid/widget/Toast$Callback;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lk/b/a/a/a/c;->onMethodCall(Lk/a/c/a/i;Lk/a/c/a/j$d;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field final synthetic a:Lk/b/a/a/a/c;


# direct methods
.method constructor <init>(Lk/b/a/a/a/c;)V
    .locals 0

    iput-object p1, p0, Lk/b/a/a/a/c$a;->a:Lk/b/a/a/a/c;

    invoke-direct {p0}, Landroid/widget/Toast$Callback;-><init>()V

    return-void
.end method


# virtual methods
.method public onToastHidden()V
    .locals 2

    invoke-super {p0}, Landroid/widget/Toast$Callback;->onToastHidden()V

    iget-object v0, p0, Lk/b/a/a/a/c$a;->a:Lk/b/a/a/a/c;

    const/4 v1, 0x0

    invoke-static {v0, v1}, Lk/b/a/a/a/c;->a(Lk/b/a/a/a/c;Landroid/widget/Toast;)V

    return-void
.end method
