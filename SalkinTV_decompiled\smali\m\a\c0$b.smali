.class final Lm/a/c0$b;
.super Ll/v/d/k;
.source ""

# interfaces
.implements Ll/v/c/p;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm/a/c0;->a(Ll/s/g;Ll/s/g;Z)Ll/s/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ll/v/d/k;",
        "Ll/v/c/p<",
        "Ll/s/g;",
        "Ll/s/g$b;",
        "Ll/s/g;",
        ">;"
    }
.end annotation


# instance fields
.field final synthetic a:Ll/v/d/q;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/v/d/q<",
            "Ll/s/g;",
            ">;"
        }
    .end annotation
.end field

.field final synthetic b:Z


# direct methods
.method constructor <init>(Ll/v/d/q;Z)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/v/d/q<",
            "Ll/s/g;",
            ">;Z)V"
        }
    .end annotation

    iput-object p1, p0, Lm/a/c0$b;->a:Ll/v/d/q;

    iput-boolean p2, p0, Lm/a/c0$b;->b:Z

    const/4 p1, 0x2

    invoke-direct {p0, p1}, Ll/v/d/k;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final a(Ll/s/g;Ll/s/g$b;)Ll/s/g;
    .locals 4

    instance-of v0, p2, Lm/a/a0;

    if-nez v0, :cond_0

    invoke-interface {p1, p2}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p1

    return-object p1

    :cond_0
    iget-object v0, p0, Lm/a/c0$b;->a:Ll/v/d/q;

    iget-object v0, v0, Ll/v/d/q;->a:Ljava/lang/Object;

    check-cast v0, Ll/s/g;

    invoke-interface {p2}, Ll/s/g$b;->getKey()Ll/s/g$c;

    move-result-object v1

    invoke-interface {v0, v1}, Ll/s/g;->get(Ll/s/g$c;)Ll/s/g$b;

    move-result-object v0

    if-nez v0, :cond_2

    iget-boolean v0, p0, Lm/a/c0$b;->b:Z

    check-cast p2, Lm/a/a0;

    if-eqz v0, :cond_1

    invoke-interface {p2}, Lm/a/a0;->p()Lm/a/a0;

    move-result-object p2

    :cond_1
    invoke-interface {p1, p2}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p1

    return-object p1

    :cond_2
    iget-object v1, p0, Lm/a/c0$b;->a:Ll/v/d/q;

    iget-object v2, v1, Ll/v/d/q;->a:Ljava/lang/Object;

    check-cast v2, Ll/s/g;

    invoke-interface {p2}, Ll/s/g$b;->getKey()Ll/s/g$c;

    move-result-object v3

    invoke-interface {v2, v3}, Ll/s/g;->minusKey(Ll/s/g$c;)Ll/s/g;

    move-result-object v2

    iput-object v2, v1, Ll/v/d/q;->a:Ljava/lang/Object;

    check-cast p2, Lm/a/a0;

    invoke-interface {p2, v0}, Lm/a/a0;->m(Ll/s/g$b;)Ll/s/g;

    move-result-object p2

    invoke-interface {p1, p2}, Ll/s/g;->plus(Ll/s/g;)Ll/s/g;

    move-result-object p1

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ll/s/g;

    check-cast p2, Ll/s/g$b;

    invoke-virtual {p0, p1, p2}, Lm/a/c0$b;->a(Ll/s/g;Ll/s/g$b;)Ll/s/g;

    move-result-object p1

    return-object p1
.end method
