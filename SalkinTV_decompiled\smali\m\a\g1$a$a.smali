.class final Lm/a/g1$a$a;
.super Ll/v/d/k;
.source ""

# interfaces
.implements Ll/v/c/l;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm/a/g1$a;-><init>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ll/v/d/k;",
        "Ll/v/c/l<",
        "Ll/s/g$b;",
        "Lm/a/g1;",
        ">;"
    }
.end annotation


# static fields
.field public static final a:Lm/a/g1$a$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/g1$a$a;

    invoke-direct {v0}, Lm/a/g1$a$a;-><init>()V

    sput-object v0, Lm/a/g1$a$a;->a:Lm/a/g1$a$a;

    return-void
.end method

.method constructor <init>()V
    .locals 1

    const/4 v0, 0x1

    invoke-direct {p0, v0}, Ll/v/d/k;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final a(Ll/s/g$b;)Lm/a/g1;
    .locals 1

    instance-of v0, p1, Lm/a/g1;

    if-eqz v0, :cond_0

    check-cast p1, Lm/a/g1;

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    :goto_0
    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ll/s/g$b;

    invoke-virtual {p0, p1}, Lm/a/g1$a$a;->a(Ll/s/g$b;)Lm/a/g1;

    move-result-object p1

    return-object p1
.end method
