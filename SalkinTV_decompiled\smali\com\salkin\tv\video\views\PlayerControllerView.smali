.class public Lcom/salkin/tv/video/views/PlayerControllerView;
.super Landroid/widget/LinearLayout;
.source ""

# interfaces
.implements Lcom/salkin/tv/video/interfaces/PlayerInfoUpdate;
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;
    }
.end annotation


# instance fields
.field private context:Landroid/content/Context;

.field private iconState:Lcom/salkin/tv/video/views/IconFontView;

.field lastTime:J

.field private listener:Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;

.field private llCenterBig:Landroid/view/View;

.field mHandler:Landroid/os/Handler;
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "HandlerLeak"
        }
    .end annotation
.end field

.field private movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

.field newPos:J

.field private player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

.field private seekBar:Landroid/widget/SeekBar;

.field private tvCurrent:Landroid/widget/TextView;

.field private tvDuratiom:Landroid/widget/TextView;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 2

    invoke-direct {p0, p1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    new-instance v0, Lcom/salkin/tv/video/views/PlayerControllerView$1;

    invoke-direct {v0, p0}, Lcom/salkin/tv/video/views/PlayerControllerView$1;-><init>(Lcom/salkin/tv/video/views/PlayerControllerView;)V

    iput-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->lastTime:J

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/views/PlayerControllerView;->initView(Landroid/content/Context;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;)V
    .locals 2

    invoke-direct {p0, p1, p2}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;)V

    new-instance p2, Lcom/salkin/tv/video/views/PlayerControllerView$1;

    invoke-direct {p2, p0}, Lcom/salkin/tv/video/views/PlayerControllerView$1;-><init>(Lcom/salkin/tv/video/views/PlayerControllerView;)V

    iput-object p2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->lastTime:J

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/views/PlayerControllerView;->initView(Landroid/content/Context;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V
    .locals 0

    invoke-direct {p0, p1, p2, p3}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;I)V

    new-instance p2, Lcom/salkin/tv/video/views/PlayerControllerView$1;

    invoke-direct {p2, p0}, Lcom/salkin/tv/video/views/PlayerControllerView$1;-><init>(Lcom/salkin/tv/video/views/PlayerControllerView;)V

    iput-object p2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const-wide/16 p2, -0x1

    iput-wide p2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p2

    iput-wide p2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->lastTime:J

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/views/PlayerControllerView;->initView(Landroid/content/Context;)V

    return-void
.end method

.method public constructor <init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V
    .locals 0

    invoke-direct {p0, p1, p2, p3, p4}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;Landroid/util/AttributeSet;II)V

    new-instance p2, Lcom/salkin/tv/video/views/PlayerControllerView$1;

    invoke-direct {p2, p0}, Lcom/salkin/tv/video/views/PlayerControllerView$1;-><init>(Lcom/salkin/tv/video/views/PlayerControllerView;)V

    iput-object p2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const-wide/16 p2, -0x1

    iput-wide p2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide p2

    iput-wide p2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->lastTime:J

    invoke-direct {p0, p1}, Lcom/salkin/tv/video/views/PlayerControllerView;->initView(Landroid/content/Context;)V

    return-void
.end method

.method private synthetic a()V
    .locals 2

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-nez v0, :cond_0

    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    goto :goto_0

    :cond_0
    invoke-direct {p0}, Lcom/salkin/tv/video/views/PlayerControllerView;->clearNewPosIfNeed()V

    :goto_0
    return-void
.end method

.method static synthetic access$000(Lcom/salkin/tv/video/views/PlayerControllerView;)Lcom/salkin/tv/video/interfaces/PlayerInterface;
    .locals 0

    iget-object p0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    return-object p0
.end method

.method static synthetic access$100(Lcom/salkin/tv/video/views/PlayerControllerView;)V
    .locals 0

    invoke-direct {p0}, Lcom/salkin/tv/video/views/PlayerControllerView;->clearNewPosIfNeed()V

    return-void
.end method

.method static synthetic access$200(Lcom/salkin/tv/video/views/PlayerControllerView;J)Ljava/lang/String;
    .locals 0

    invoke-direct {p0, p1, p2}, Lcom/salkin/tv/video/views/PlayerControllerView;->buildTimeMilli(J)Ljava/lang/String;

    move-result-object p0

    return-object p0
.end method

.method static synthetic access$300(Lcom/salkin/tv/video/views/PlayerControllerView;)Landroid/widget/TextView;
    .locals 0

    iget-object p0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->tvCurrent:Landroid/widget/TextView;

    return-object p0
.end method

.method static synthetic access$400(Lcom/salkin/tv/video/views/PlayerControllerView;)Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;
    .locals 0

    iget-object p0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->listener:Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;

    return-object p0
.end method

.method private buildTimeMilli(J)Ljava/lang/String;
    .locals 13

    const-wide/16 v0, 0x3e8

    div-long v0, p1, v0

    const-wide/16 v2, 0xe10

    div-long v4, v0, v2

    rem-long v2, v0, v2

    const-wide/16 v6, 0x3c

    div-long/2addr v2, v6

    rem-long/2addr v0, v6

    const-wide/16 v6, 0x0

    cmp-long v8, p1, v6

    if-gtz v8, :cond_0

    const-string p1, "00:00"

    return-object p1

    :cond_0
    const-wide/16 p1, 0x64

    const/4 v8, 0x3

    const/4 v9, 0x2

    const/4 v10, 0x1

    const/4 v11, 0x0

    cmp-long v12, v4, p1

    if-ltz v12, :cond_1

    sget-object p1, Ljava/util/Locale;->US:Ljava/util/Locale;

    new-array p2, v8, [Ljava/lang/Object;

    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v4

    aput-object v4, p2, v11

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    aput-object v2, p2, v10

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    aput-object v0, p2, v9

    const-string v0, "%d:%02d:%02d"

    invoke-static {p1, v0, p2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_1
    cmp-long p1, v4, v6

    if-lez p1, :cond_2

    sget-object p1, Ljava/util/Locale;->US:Ljava/util/Locale;

    new-array p2, v8, [Ljava/lang/Object;

    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v4

    aput-object v4, p2, v11

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    aput-object v2, p2, v10

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    aput-object v0, p2, v9

    const-string v0, "%02d:%02d:%02d"

    invoke-static {p1, v0, p2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1

    :cond_2
    sget-object p1, Ljava/util/Locale;->US:Ljava/util/Locale;

    new-array p2, v9, [Ljava/lang/Object;

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v2

    aput-object v2, p2, v11

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    aput-object v0, p2, v10

    const-string v0, "%02d:%02d"

    invoke-static {p1, v0, p2}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method private clearNewPosIfNeed()V
    .locals 4

    new-instance v0, Lcom/salkin/tv/video/widgets/Settings;

    iget-object v1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->context:Landroid/content/Context;

    invoke-direct {v0, v1}, Lcom/salkin/tv/video/widgets/Settings;-><init>(Landroid/content/Context;)V

    invoke-virtual {v0}, Lcom/salkin/tv/video/widgets/Settings;->getPlayer()I

    move-result v0

    const/4 v1, 0x2

    if-ne v0, v1, :cond_0

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const/4 v1, 0x5

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    new-instance v1, Lcom/salkin/tv/video/views/a;

    invoke-direct {v1, p0}, Lcom/salkin/tv/video/views/a;-><init>(Lcom/salkin/tv/video/views/PlayerControllerView;)V

    const-wide/16 v2, 0xbb8

    invoke-virtual {v0, v1, v2, v3}, Landroid/os/Handler;->postDelayed(Ljava/lang/Runnable;J)Z

    :cond_0
    return-void
.end method

.method private initView(Landroid/content/Context;)V
    .locals 2

    iput-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->context:Landroid/content/Context;

    invoke-static {p1}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    move-result-object p1

    const v0, 0x7f0c002a

    const/4 v1, 0x1

    invoke-virtual {p1, v0, p0, v1}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;Z)Landroid/view/View;

    const p1, 0x7f0900a7

    invoke-virtual {p0, p1}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Landroid/widget/SeekBar;

    iput-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->seekBar:Landroid/widget/SeekBar;

    const p1, 0x7f0900d3

    invoke-virtual {p0, p1}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Landroid/widget/TextView;

    iput-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->tvCurrent:Landroid/widget/TextView;

    const p1, 0x7f0900d5

    invoke-virtual {p0, p1}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Landroid/widget/TextView;

    iput-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->tvDuratiom:Landroid/widget/TextView;

    const p1, 0x7f09005f

    invoke-virtual {p0, p1}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Lcom/salkin/tv/video/views/IconFontView;

    iput-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->iconState:Lcom/salkin/tv/video/views/IconFontView;

    const p1, 0x7f090073

    invoke-virtual {p0, p1}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    iput-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->llCenterBig:Landroid/view/View;

    const p1, 0x7f090092

    invoke-virtual {p0, p1}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, p0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->seekBar:Landroid/widget/SeekBar;

    new-instance v0, Lcom/salkin/tv/video/views/PlayerControllerView$2;

    invoke-direct {v0, p0}, Lcom/salkin/tv/video/views/PlayerControllerView$2;-><init>(Lcom/salkin/tv/video/views/PlayerControllerView;)V

    invoke-virtual {p1, v0}, Landroid/widget/SeekBar;->setOnSeekBarChangeListener(Landroid/widget/SeekBar$OnSeekBarChangeListener;)V

    return-void
.end method


# virtual methods
.method public synthetic b()V
    .locals 0

    invoke-direct {p0}, Lcom/salkin/tv/video/views/PlayerControllerView;->a()V

    return-void
.end method

.method public onClick(Landroid/view/View;)V
    .locals 1

    invoke-virtual {p1}, Landroid/view/View;->getId()I

    move-result p1

    const v0, 0x7f090073

    if-eq p1, v0, :cond_1

    const v0, 0x7f090092

    if-eq p1, v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->listener:Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;

    invoke-interface {p1}, Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;->onRightViewMenuClick()V

    goto :goto_0

    :cond_1
    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->listener:Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;

    invoke-interface {p1}, Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;->onCenterAreaClick()V

    :goto_0
    return-void
.end method

.method protected onDetachedFromWindow()V
    .locals 2

    invoke-super {p0}, Landroid/widget/LinearLayout;->onDetachedFromWindow()V

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const/4 v1, 0x4

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const/4 v1, 0x5

    invoke-virtual {v0, v1}, Landroid/os/Handler;->removeMessages(I)V

    return-void
.end method

.method public onPlayCompleted()V
    .locals 1

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->listener:Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;

    if-eqz v0, :cond_0

    invoke-interface {v0}, Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;->onPlayCompleted()V

    :cond_0
    return-void
.end method

.method public onSeekCompleted()V
    .locals 2

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-nez v0, :cond_0

    const-wide/16 v0, -0x1

    iput-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    :cond_0
    return-void
.end method

.method public seekByKeyCode(I)V
    .locals 6

    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->lastTime:J

    sub-long/2addr v0, v2

    const-wide/16 v2, 0x64

    cmp-long v4, v0, v2

    if-gez v4, :cond_0

    return-void

    :cond_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->lastTime:J

    iget-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-gez v4, :cond_1

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {v0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->getCurrentPosition()J

    move-result-wide v0

    iput-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    :cond_1
    const/16 v0, 0x16

    const-wide/16 v1, 0x7530

    iget-wide v3, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    if-ne p1, v0, :cond_2

    add-long/2addr v3, v1

    goto :goto_0

    :cond_2
    sub-long/2addr v3, v1

    :goto_0
    iput-wide v3, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {p1}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->getDuration()J

    move-result-wide v0

    iget-wide v2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    const-wide/16 v4, 0x1388

    sub-long/2addr v0, v4

    cmp-long p1, v2, v0

    if-ltz p1, :cond_3

    iput-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    :cond_3
    iget-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    const-wide/16 v2, 0x3e8

    cmp-long p1, v0, v2

    if-gez p1, :cond_4

    iput-wide v2, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    :cond_4
    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->seekBar:Landroid/widget/SeekBar;

    iget-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    long-to-int v1, v0

    invoke-virtual {p1, v1}, Landroid/widget/SeekBar;->setProgress(I)V

    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->tvCurrent:Landroid/widget/TextView;

    iget-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    invoke-direct {p0, v0, v1}, Lcom/salkin/tv/video/views/PlayerControllerView;->buildTimeMilli(J)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Landroid/os/Handler;->removeMessages(I)V

    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const-wide/16 v1, 0x258

    invoke-virtual {p1, v0, v1, v2}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    invoke-direct {p0}, Lcom/salkin/tv/video/views/PlayerControllerView;->clearNewPosIfNeed()V

    return-void
.end method

.method public setListener(Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;)V
    .locals 0

    iput-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->listener:Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;

    return-void
.end method

.method public setMovieData(Lcom/salkin/tv/video/entity/MovieDataEntity;)V
    .locals 1

    iput-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->movieData:Lcom/salkin/tv/video/entity/MovieDataEntity;

    const v0, 0x7f0900d2

    invoke-virtual {p0, v0}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object v0

    check-cast v0, Landroid/widget/TextView;

    iget-object p1, p1, Lcom/salkin/tv/video/entity/MovieDataEntity;->title:Ljava/lang/String;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public setPlayer(Lcom/salkin/tv/video/interfaces/PlayerInterface;)V
    .locals 0

    iput-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {p1, p0}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->setInfoUpdateListener(Lcom/salkin/tv/video/interfaces/PlayerInfoUpdate;)V

    return-void
.end method

.method public updateBuffer(J)V
    .locals 1

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->seekBar:Landroid/widget/SeekBar;

    long-to-int p2, p1

    invoke-virtual {v0, p2}, Landroid/widget/SeekBar;->setSecondaryProgress(I)V

    return-void
.end method

.method public updateCurrent(J)V
    .locals 5

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const/4 v1, 0x4

    invoke-virtual {v0, v1}, Landroid/os/Handler;->hasMessages(I)Z

    move-result v0

    if-nez v0, :cond_0

    new-instance v0, Landroid/os/Message;

    invoke-direct {v0}, Landroid/os/Message;-><init>()V

    iput v1, v0, Landroid/os/Message;->what:I

    invoke-static {p1, p2}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    iput-object v1, v0, Landroid/os/Message;->obj:Ljava/lang/Object;

    iget-object v1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->mHandler:Landroid/os/Handler;

    const-wide/16 v2, 0x1388

    invoke-virtual {v1, v0, v2, v3}, Landroid/os/Handler;->sendMessageDelayed(Landroid/os/Message;J)Z

    :cond_0
    iget-wide v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->newPos:J

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-lez v4, :cond_1

    return-void

    :cond_1
    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->seekBar:Landroid/widget/SeekBar;

    long-to-int v1, p1

    invoke-virtual {v0, v1}, Landroid/widget/SeekBar;->setProgress(I)V

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->tvCurrent:Landroid/widget/TextView;

    invoke-direct {p0, p1, p2}, Lcom/salkin/tv/video/views/PlayerControllerView;->buildTimeMilli(J)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    invoke-static {}, Lcom/salkin/tv/video/MyApp;->getInstance()Lcom/salkin/tv/video/MyApp;

    move-result-object v0

    invoke-virtual {v0}, Lcom/salkin/tv/video/MyApp;->getPlayingItem()Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;

    move-result-object v0

    if-eqz v0, :cond_2

    iget-object v1, v0, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;->type:Ljava/lang/String;

    const-string v2, "1"

    invoke-virtual {v1, v2}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_2

    iget v0, v0, Lcom/salkin/tv/video/entity/MovieDataEntity$ChildListBean;->free_time:I

    int-to-long v0, v0

    const-wide/16 v2, 0x3e8

    div-long/2addr p1, v2

    cmp-long v2, v0, p1

    # 修改条件，永远不触发VIP提示
    if-gez v2, :cond_2

    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->listener:Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;

    invoke-interface {p1}, Lcom/salkin/tv/video/views/PlayerControllerView$PlayerControllerViewInterface;->showVip()V

    :cond_2
    return-void
.end method

.method public updateDuration(J)V
    .locals 2

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->seekBar:Landroid/widget/SeekBar;

    long-to-int v1, p1

    invoke-virtual {v0, v1}, Landroid/widget/SeekBar;->setMax(I)V

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->tvDuratiom:Landroid/widget/TextView;

    invoke-direct {p0, p1, p2}, Lcom/salkin/tv/video/views/PlayerControllerView;->buildTimeMilli(J)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public updatePlayerState(I)V
    .locals 5
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "SetTextI18n"
        }
    .end annotation

    const/4 v0, 0x1

    const/4 v1, 0x0

    const v2, 0x7f090074

    const/4 v3, 0x4

    const v4, 0x7f09008e

    if-ne p1, v0, :cond_0

    invoke-virtual {p0, v4}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    invoke-virtual {p0, v2}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    goto :goto_0

    :cond_0
    invoke-virtual {p0, v4}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, v3}, Landroid/view/View;->setVisibility(I)V

    invoke-virtual {p0, v2}, Landroid/widget/LinearLayout;->findViewById(I)Landroid/view/View;

    move-result-object p1

    invoke-virtual {p1, v1}, Landroid/view/View;->setVisibility(I)V

    :goto_0
    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {p1}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->isPlaying()Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->iconState:Lcom/salkin/tv/video/views/IconFontView;

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->context:Landroid/content/Context;

    const v1, 0x7f0f004c

    goto :goto_1

    :cond_1
    iget-object p1, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->iconState:Lcom/salkin/tv/video/views/IconFontView;

    iget-object v0, p0, Lcom/salkin/tv/video/views/PlayerControllerView;->context:Landroid/content/Context;

    const v1, 0x7f0f004b

    :goto_1
    invoke-virtual {v0, v1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method
