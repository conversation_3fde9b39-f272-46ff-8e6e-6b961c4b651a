.class public abstract Ll/v/d/m;
.super Ll/v/d/o;
.source ""

# interfaces
.implements Ll/y/f;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V
    .locals 0

    invoke-direct/range {p0 .. p5}, Ll/v/d/o;-><init>(Ljava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    return-void
.end method


# virtual methods
.method protected computeReflected()Ll/y/b;
    .locals 0

    invoke-static {p0}, Ll/v/d/r;->d(Ll/v/d/m;)Ll/y/f;

    return-object p0
.end method

.method public invoke()Ljava/lang/Object;
    .locals 1

    invoke-interface {p0}, Ll/y/f;->f()Ljava/lang/Object;

    move-result-object v0

    return-object v0
.end method
