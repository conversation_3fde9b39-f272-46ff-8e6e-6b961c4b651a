.class public Ln/v;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/lang/Cloneable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ln/v$b;
    }
.end annotation


# static fields
.field static final B:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/w;",
            ">;"
        }
    .end annotation
.end field

.field static final C:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/k;",
            ">;"
        }
    .end annotation
.end field


# instance fields
.field final A:I

.field final a:Ln/n;

.field final b:Ljava/net/Proxy;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field final c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/w;",
            ">;"
        }
    .end annotation
.end field

.field final d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/k;",
            ">;"
        }
    .end annotation
.end field

.field final e:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/t;",
            ">;"
        }
    .end annotation
.end field

.field final f:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/t;",
            ">;"
        }
    .end annotation
.end field

.field final g:Ln/p$c;

.field final h:Ljava/net/ProxySelector;

.field final i:Ln/m;

.field final j:Ln/c;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field final k:Ln/e0/e/d;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field final l:Ljavax/net/SocketFactory;

.field final m:Ljavax/net/ssl/SSLSocketFactory;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field final n:Ln/e0/k/c;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field final o:Ljavax/net/ssl/HostnameVerifier;

.field final p:Ln/g;

.field final q:Ln/b;

.field final r:Ln/b;

.field final s:Ln/j;

.field final t:Ln/o;

.field final u:Z

.field final v:Z

.field final w:Z

.field final x:I

.field final y:I

.field final z:I


# direct methods
.method static constructor <clinit>()V
    .locals 5

    const/4 v0, 0x2

    new-array v1, v0, [Ln/w;

    sget-object v2, Ln/w;->e:Ln/w;

    const/4 v3, 0x0

    aput-object v2, v1, v3

    sget-object v2, Ln/w;->c:Ln/w;

    const/4 v4, 0x1

    aput-object v2, v1, v4

    invoke-static {v1}, Ln/e0/c;->u([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v1

    sput-object v1, Ln/v;->B:Ljava/util/List;

    new-array v0, v0, [Ln/k;

    sget-object v1, Ln/k;->g:Ln/k;

    aput-object v1, v0, v3

    sget-object v1, Ln/k;->h:Ln/k;

    aput-object v1, v0, v4

    invoke-static {v0}, Ln/e0/c;->u([Ljava/lang/Object;)Ljava/util/List;

    move-result-object v0

    sput-object v0, Ln/v;->C:Ljava/util/List;

    new-instance v0, Ln/v$a;

    invoke-direct {v0}, Ln/v$a;-><init>()V

    sput-object v0, Ln/e0/a;->a:Ln/e0/a;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    new-instance v0, Ln/v$b;

    invoke-direct {v0}, Ln/v$b;-><init>()V

    invoke-direct {p0, v0}, Ln/v;-><init>(Ln/v$b;)V

    return-void
.end method

.method constructor <init>(Ln/v$b;)V
    .locals 4

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iget-object v0, p1, Ln/v$b;->a:Ln/n;

    iput-object v0, p0, Ln/v;->a:Ln/n;

    iget-object v0, p1, Ln/v$b;->b:Ljava/net/Proxy;

    iput-object v0, p0, Ln/v;->b:Ljava/net/Proxy;

    iget-object v0, p1, Ln/v$b;->c:Ljava/util/List;

    iput-object v0, p0, Ln/v;->c:Ljava/util/List;

    iget-object v0, p1, Ln/v$b;->d:Ljava/util/List;

    iput-object v0, p0, Ln/v;->d:Ljava/util/List;

    iget-object v1, p1, Ln/v$b;->e:Ljava/util/List;

    invoke-static {v1}, Ln/e0/c;->t(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Ln/v;->e:Ljava/util/List;

    iget-object v1, p1, Ln/v$b;->f:Ljava/util/List;

    invoke-static {v1}, Ln/e0/c;->t(Ljava/util/List;)Ljava/util/List;

    move-result-object v1

    iput-object v1, p0, Ln/v;->f:Ljava/util/List;

    iget-object v1, p1, Ln/v$b;->g:Ln/p$c;

    iput-object v1, p0, Ln/v;->g:Ln/p$c;

    iget-object v1, p1, Ln/v$b;->h:Ljava/net/ProxySelector;

    iput-object v1, p0, Ln/v;->h:Ljava/net/ProxySelector;

    iget-object v1, p1, Ln/v$b;->i:Ln/m;

    iput-object v1, p0, Ln/v;->i:Ln/m;

    iget-object v1, p1, Ln/v$b;->j:Ln/c;

    iget-object v1, p1, Ln/v$b;->k:Ln/e0/e/d;

    iput-object v1, p0, Ln/v;->k:Ln/e0/e/d;

    iget-object v1, p1, Ln/v$b;->l:Ljavax/net/SocketFactory;

    iput-object v1, p0, Ln/v;->l:Ljavax/net/SocketFactory;

    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v0

    const/4 v1, 0x0

    :cond_0
    const/4 v2, 0x0

    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    move-result v3

    if-eqz v3, :cond_2

    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v3

    check-cast v3, Ln/k;

    if-nez v2, :cond_1

    invoke-virtual {v3}, Ln/k;->d()Z

    move-result v2

    if-eqz v2, :cond_0

    :cond_1
    const/4 v2, 0x1

    goto :goto_0

    :cond_2
    iget-object v0, p1, Ln/v$b;->m:Ljavax/net/ssl/SSLSocketFactory;

    if-nez v0, :cond_4

    if-nez v2, :cond_3

    goto :goto_1

    :cond_3
    invoke-static {}, Ln/e0/c;->C()Ljavax/net/ssl/X509TrustManager;

    move-result-object v0

    invoke-static {v0}, Ln/v;->r(Ljavax/net/ssl/X509TrustManager;)Ljavax/net/ssl/SSLSocketFactory;

    move-result-object v1

    iput-object v1, p0, Ln/v;->m:Ljavax/net/ssl/SSLSocketFactory;

    invoke-static {v0}, Ln/e0/k/c;->b(Ljavax/net/ssl/X509TrustManager;)Ln/e0/k/c;

    move-result-object v0

    goto :goto_2

    :cond_4
    :goto_1
    iput-object v0, p0, Ln/v;->m:Ljavax/net/ssl/SSLSocketFactory;

    iget-object v0, p1, Ln/v$b;->n:Ln/e0/k/c;

    :goto_2
    iput-object v0, p0, Ln/v;->n:Ln/e0/k/c;

    iget-object v0, p0, Ln/v;->m:Ljavax/net/ssl/SSLSocketFactory;

    if-eqz v0, :cond_5

    invoke-static {}, Ln/e0/j/f;->j()Ln/e0/j/f;

    move-result-object v0

    iget-object v1, p0, Ln/v;->m:Ljavax/net/ssl/SSLSocketFactory;

    invoke-virtual {v0, v1}, Ln/e0/j/f;->f(Ljavax/net/ssl/SSLSocketFactory;)V

    :cond_5
    iget-object v0, p1, Ln/v$b;->o:Ljavax/net/ssl/HostnameVerifier;

    iput-object v0, p0, Ln/v;->o:Ljavax/net/ssl/HostnameVerifier;

    iget-object v0, p1, Ln/v$b;->p:Ln/g;

    iget-object v1, p0, Ln/v;->n:Ln/e0/k/c;

    invoke-virtual {v0, v1}, Ln/g;->f(Ln/e0/k/c;)Ln/g;

    move-result-object v0

    iput-object v0, p0, Ln/v;->p:Ln/g;

    iget-object v0, p1, Ln/v$b;->q:Ln/b;

    iput-object v0, p0, Ln/v;->q:Ln/b;

    iget-object v0, p1, Ln/v$b;->r:Ln/b;

    iput-object v0, p0, Ln/v;->r:Ln/b;

    iget-object v0, p1, Ln/v$b;->s:Ln/j;

    iput-object v0, p0, Ln/v;->s:Ln/j;

    iget-object v0, p1, Ln/v$b;->t:Ln/o;

    iput-object v0, p0, Ln/v;->t:Ln/o;

    iget-boolean v0, p1, Ln/v$b;->u:Z

    iput-boolean v0, p0, Ln/v;->u:Z

    iget-boolean v0, p1, Ln/v$b;->v:Z

    iput-boolean v0, p0, Ln/v;->v:Z

    iget-boolean v0, p1, Ln/v$b;->w:Z

    iput-boolean v0, p0, Ln/v;->w:Z

    iget v0, p1, Ln/v$b;->x:I

    iput v0, p0, Ln/v;->x:I

    iget v0, p1, Ln/v$b;->y:I

    iput v0, p0, Ln/v;->y:I

    iget v0, p1, Ln/v$b;->z:I

    iput v0, p0, Ln/v;->z:I

    iget p1, p1, Ln/v$b;->A:I

    iput p1, p0, Ln/v;->A:I

    iget-object p1, p0, Ln/v;->e:Ljava/util/List;

    const/4 v0, 0x0

    invoke-interface {p1, v0}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_7

    iget-object p1, p0, Ln/v;->f:Ljava/util/List;

    invoke-interface {p1, v0}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    move-result p1

    if-nez p1, :cond_6

    return-void

    :cond_6
    new-instance p1, Ljava/lang/IllegalStateException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Null network interceptor: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Ln/v;->f:Ljava/util/List;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_7
    new-instance p1, Ljava/lang/IllegalStateException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Null interceptor: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Ln/v;->e:Ljava/util/List;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    goto :goto_4

    :goto_3
    throw p1

    :goto_4
    goto :goto_3
.end method

.method private static r(Ljavax/net/ssl/X509TrustManager;)Ljavax/net/ssl/SSLSocketFactory;
    .locals 3

    :try_start_0
    invoke-static {}, Ln/e0/j/f;->j()Ln/e0/j/f;

    move-result-object v0

    invoke-virtual {v0}, Ln/e0/j/f;->k()Ljavax/net/ssl/SSLContext;

    move-result-object v0

    const/4 v1, 0x1

    new-array v1, v1, [Ljavax/net/ssl/TrustManager;

    const/4 v2, 0x0

    aput-object p0, v1, v2

    const/4 p0, 0x0

    invoke-virtual {v0, p0, v1, p0}, Ljavax/net/ssl/SSLContext;->init([Ljavax/net/ssl/KeyManager;[Ljavax/net/ssl/TrustManager;Ljava/security/SecureRandom;)V

    invoke-virtual {v0}, Ljavax/net/ssl/SSLContext;->getSocketFactory()Ljavax/net/ssl/SSLSocketFactory;

    move-result-object p0
    :try_end_0
    .catch Ljava/security/GeneralSecurityException; {:try_start_0 .. :try_end_0} :catch_0

    return-object p0

    :catch_0
    move-exception p0

    const-string v0, "No System TLS"

    invoke-static {v0, p0}, Ln/e0/c;->b(Ljava/lang/String;Ljava/lang/Exception;)Ljava/lang/AssertionError;

    move-result-object p0

    throw p0
.end method


# virtual methods
.method public A()Ljavax/net/ssl/SSLSocketFactory;
    .locals 1

    iget-object v0, p0, Ln/v;->m:Ljavax/net/ssl/SSLSocketFactory;

    return-object v0
.end method

.method public B()I
    .locals 1

    iget v0, p0, Ln/v;->z:I

    return v0
.end method

.method public a()Ln/b;
    .locals 1

    iget-object v0, p0, Ln/v;->r:Ln/b;

    return-object v0
.end method

.method public b()Ln/g;
    .locals 1

    iget-object v0, p0, Ln/v;->p:Ln/g;

    return-object v0
.end method

.method public c()I
    .locals 1

    iget v0, p0, Ln/v;->x:I

    return v0
.end method

.method public d()Ln/j;
    .locals 1

    iget-object v0, p0, Ln/v;->s:Ln/j;

    return-object v0
.end method

.method public e()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ln/k;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Ln/v;->d:Ljava/util/List;

    return-object v0
.end method

.method public f()Ln/m;
    .locals 1

    iget-object v0, p0, Ln/v;->i:Ln/m;

    return-object v0
.end method

.method public g()Ln/n;
    .locals 1

    iget-object v0, p0, Ln/v;->a:Ln/n;

    return-object v0
.end method

.method public h()Ln/o;
    .locals 1

    iget-object v0, p0, Ln/v;->t:Ln/o;

    return-object v0
.end method

.method public i()Ln/p$c;
    .locals 1

    iget-object v0, p0, Ln/v;->g:Ln/p$c;

    return-object v0
.end method

.method public j()Z
    .locals 1

    iget-boolean v0, p0, Ln/v;->v:Z

    return v0
.end method

.method public k()Z
    .locals 1

    iget-boolean v0, p0, Ln/v;->u:Z

    return v0
.end method

.method public l()Ljavax/net/ssl/HostnameVerifier;
    .locals 1

    iget-object v0, p0, Ln/v;->o:Ljavax/net/ssl/HostnameVerifier;

    return-object v0
.end method

.method public m()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ln/t;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Ln/v;->e:Ljava/util/List;

    return-object v0
.end method

.method n()Ln/e0/e/d;
    .locals 1

    iget-object v0, p0, Ln/v;->j:Ln/c;

    if-eqz v0, :cond_0

    iget-object v0, v0, Ln/c;->a:Ln/e0/e/d;

    goto :goto_0

    :cond_0
    iget-object v0, p0, Ln/v;->k:Ln/e0/e/d;

    :goto_0
    return-object v0
.end method

.method public o()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ln/t;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Ln/v;->f:Ljava/util/List;

    return-object v0
.end method

.method public p()Ln/v$b;
    .locals 1

    new-instance v0, Ln/v$b;

    invoke-direct {v0, p0}, Ln/v$b;-><init>(Ln/v;)V

    return-object v0
.end method

.method public q(Ln/y;)Ln/e;
    .locals 1

    const/4 v0, 0x0

    invoke-static {p0, p1, v0}, Ln/x;->f(Ln/v;Ln/y;Z)Ln/x;

    move-result-object p1

    return-object p1
.end method

.method public s()I
    .locals 1

    iget v0, p0, Ln/v;->A:I

    return v0
.end method

.method public t()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Ln/w;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Ln/v;->c:Ljava/util/List;

    return-object v0
.end method

.method public u()Ljava/net/Proxy;
    .locals 1

    iget-object v0, p0, Ln/v;->b:Ljava/net/Proxy;

    return-object v0
.end method

.method public v()Ln/b;
    .locals 1

    iget-object v0, p0, Ln/v;->q:Ln/b;

    return-object v0
.end method

.method public w()Ljava/net/ProxySelector;
    .locals 1

    iget-object v0, p0, Ln/v;->h:Ljava/net/ProxySelector;

    return-object v0
.end method

.method public x()I
    .locals 1

    iget v0, p0, Ln/v;->y:I

    return v0
.end method

.method public y()Z
    .locals 1

    iget-boolean v0, p0, Ln/v;->w:Z

    return v0
.end method

.method public z()Ljavax/net/SocketFactory;
    .locals 1

    iget-object v0, p0, Ln/v;->l:Ljavax/net/SocketFactory;

    return-object v0
.end method
