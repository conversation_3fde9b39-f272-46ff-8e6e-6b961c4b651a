.class final Lm/a/p2/j0$b;
.super Ll/v/d/k;
.source ""

# interfaces
.implements Ll/v/c/p;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm/a/p2/j0;-><clinit>()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ll/v/d/k;",
        "Ll/v/c/p<",
        "Lm/a/h2<",
        "*>;",
        "Ll/s/g$b;",
        "Lm/a/h2<",
        "*>;>;"
    }
.end annotation


# static fields
.field public static final a:Lm/a/p2/j0$b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/p2/j0$b;

    invoke-direct {v0}, Lm/a/p2/j0$b;-><init>()V

    sput-object v0, Lm/a/p2/j0$b;->a:Lm/a/p2/j0$b;

    return-void
.end method

.method constructor <init>()V
    .locals 1

    const/4 v0, 0x2

    invoke-direct {p0, v0}, Ll/v/d/k;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final a(Lm/a/h2;Ll/s/g$b;)Lm/a/h2;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lm/a/h2<",
            "*>;",
            "Ll/s/g$b;",
            ")",
            "Lm/a/h2<",
            "*>;"
        }
    .end annotation

    if-eqz p1, :cond_0

    return-object p1

    :cond_0
    instance-of p1, p2, Lm/a/h2;

    if-eqz p1, :cond_1

    check-cast p2, Lm/a/h2;

    goto :goto_0

    :cond_1
    const/4 p2, 0x0

    :goto_0
    return-object p2
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Lm/a/h2;

    check-cast p2, Ll/s/g$b;

    invoke-virtual {p0, p1, p2}, Lm/a/p2/j0$b;->a(Lm/a/h2;Ll/s/g$b;)Lm/a/h2;

    move-result-object p1

    return-object p1
.end method
