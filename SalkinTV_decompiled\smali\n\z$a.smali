.class Ln/z$a;
.super Ln/z;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ln/z;->e(Ln/u;[BII)Ln/z;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic a:Ln/u;

.field final synthetic b:I

.field final synthetic c:[B

.field final synthetic d:I


# direct methods
.method constructor <init>(Ln/u;I[BI)V
    .locals 0

    iput-object p1, p0, Ln/z$a;->a:Ln/u;

    iput p2, p0, Ln/z$a;->b:I

    iput-object p3, p0, Ln/z$a;->c:[B

    iput p4, p0, Ln/z$a;->d:I

    invoke-direct {p0}, Ln/z;-><init>()V

    return-void
.end method


# virtual methods
.method public a()J
    .locals 2

    iget v0, p0, Ln/z$a;->b:I

    int-to-long v0, v0

    return-wide v0
.end method

.method public b()Ln/u;
    .locals 1
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Ln/z$a;->a:Ln/u;

    return-object v0
.end method

.method public f(Lo/d;)V
    .locals 3

    iget-object v0, p0, Ln/z$a;->c:[B

    iget v1, p0, Ln/z$a;->d:I

    iget v2, p0, Ln/z$a;->b:I

    invoke-interface {p1, v0, v1, v2}, Lo/d;->write([BII)Lo/d;

    return-void
.end method
