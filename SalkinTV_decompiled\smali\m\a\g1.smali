.class public abstract Lm/a/g1;
.super Lm/a/d0;
.source ""

# interfaces
.implements Ljava/io/Closeable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lm/a/g1$a;
    }
.end annotation


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lm/a/g1$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lm/a/g1$a;-><init>(Ll/v/d/e;)V

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lm/a/d0;-><init>()V

    return-void
.end method
