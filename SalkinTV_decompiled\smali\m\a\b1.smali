.class public abstract Lm/a/b1;
.super Lm/a/c1;
.source ""

# interfaces
.implements Lm/a/r0;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lm/a/b1$a;,
        Lm/a/b1$b;
    }
.end annotation


# static fields
.field private static final synthetic e:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

.field private static final synthetic f:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;


# instance fields
.field private volatile synthetic _delayed:Ljava/lang/Object;

.field private volatile synthetic _isCompleted:I

.field private volatile synthetic _queue:Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    const-class v0, Ljava/lang/Object;

    const-class v1, Lm/a/b1;

    const-string v2, "_queue"

    invoke-static {v1, v0, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->newUpdater(Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    move-result-object v2

    sput-object v2, Lm/a/b1;->e:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    const-string v2, "_delayed"

    invoke-static {v1, v0, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->newUpdater(Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    move-result-object v0

    sput-object v0, Lm/a/b1;->f:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Lm/a/c1;-><init>()V

    const/4 v0, 0x0

    iput-object v0, p0, Lm/a/b1;->_queue:Ljava/lang/Object;

    iput-object v0, p0, Lm/a/b1;->_delayed:Ljava/lang/Object;

    const/4 v0, 0x0

    iput v0, p0, Lm/a/b1;->_isCompleted:I

    return-void
.end method

.method public static final synthetic i0(Lm/a/b1;)Z
    .locals 0

    invoke-direct {p0}, Lm/a/b1;->n0()Z

    move-result p0

    return p0
.end method

.method private final j0()V
    .locals 4

    invoke-static {}, Lm/a/n0;->a()Z

    move-result v0

    if-eqz v0, :cond_1

    invoke-direct {p0}, Lm/a/b1;->n0()Z

    move-result v0

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_1
    :goto_0
    iget-object v0, p0, Lm/a/b1;->_queue:Ljava/lang/Object;

    if-nez v0, :cond_2

    sget-object v0, Lm/a/b1;->e:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    const/4 v1, 0x0

    invoke-static {}, Lm/a/e1;->a()Lm/a/p2/f0;

    move-result-object v2

    invoke-virtual {v0, p0, v1, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_2
    instance-of v1, v0, Lm/a/p2/u;

    if-eqz v1, :cond_3

    check-cast v0, Lm/a/p2/u;

    invoke-virtual {v0}, Lm/a/p2/u;->d()Z

    return-void

    :cond_3
    invoke-static {}, Lm/a/e1;->a()Lm/a/p2/f0;

    move-result-object v1

    if-ne v0, v1, :cond_4

    return-void

    :cond_4
    new-instance v1, Lm/a/p2/u;

    const/16 v2, 0x8

    const/4 v3, 0x1

    invoke-direct {v1, v2, v3}, Lm/a/p2/u;-><init>(IZ)V

    if-eqz v0, :cond_5

    move-object v2, v0

    check-cast v2, Ljava/lang/Runnable;

    invoke-virtual {v1, v2}, Lm/a/p2/u;->a(Ljava/lang/Object;)I

    sget-object v2, Lm/a/b1;->e:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v2, p0, v0, v1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_1

    return-void

    :cond_5
    new-instance v0, Ljava/lang/NullPointerException;

    const-string v1, "null cannot be cast to non-null type java.lang.Runnable{ kotlinx.coroutines.RunnableKt.Runnable }"

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    goto :goto_2

    :goto_1
    throw v0

    :goto_2
    goto :goto_1
.end method

.method private final k0()Ljava/lang/Runnable;
    .locals 4

    :cond_0
    :goto_0
    iget-object v0, p0, Lm/a/b1;->_queue:Ljava/lang/Object;

    const/4 v1, 0x0

    if-nez v0, :cond_1

    return-object v1

    :cond_1
    instance-of v2, v0, Lm/a/p2/u;

    if-eqz v2, :cond_4

    if-eqz v0, :cond_3

    move-object v1, v0

    check-cast v1, Lm/a/p2/u;

    invoke-virtual {v1}, Lm/a/p2/u;->j()Ljava/lang/Object;

    move-result-object v2

    sget-object v3, Lm/a/p2/u;->h:Lm/a/p2/f0;

    if-eq v2, v3, :cond_2

    check-cast v2, Ljava/lang/Runnable;

    return-object v2

    :cond_2
    sget-object v2, Lm/a/b1;->e:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v1}, Lm/a/p2/u;->i()Lm/a/p2/u;

    move-result-object v1

    invoke-virtual {v2, p0, v0, v1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    goto :goto_0

    :cond_3
    new-instance v0, Ljava/lang/NullPointerException;

    const-string v1, "null cannot be cast to non-null type kotlinx.coroutines.internal.LockFreeTaskQueueCore<java.lang.Runnable{ kotlinx.coroutines.RunnableKt.Runnable }>{ kotlinx.coroutines.EventLoop_commonKt.Queue<java.lang.Runnable{ kotlinx.coroutines.RunnableKt.Runnable }> }"

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw v0

    :cond_4
    invoke-static {}, Lm/a/e1;->a()Lm/a/p2/f0;

    move-result-object v2

    if-ne v0, v2, :cond_5

    return-object v1

    :cond_5
    sget-object v2, Lm/a/b1;->e:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v2, p0, v0, v1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    if-eqz v0, :cond_6

    check-cast v0, Ljava/lang/Runnable;

    return-object v0

    :cond_6
    new-instance v0, Ljava/lang/NullPointerException;

    const-string v1, "null cannot be cast to non-null type java.lang.Runnable{ kotlinx.coroutines.RunnableKt.Runnable }"

    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    goto :goto_2

    :goto_1
    throw v0

    :goto_2
    goto :goto_1
.end method

.method private final m0(Ljava/lang/Runnable;)Z
    .locals 5

    :cond_0
    :goto_0
    iget-object v0, p0, Lm/a/b1;->_queue:Ljava/lang/Object;

    invoke-direct {p0}, Lm/a/b1;->n0()Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_1

    return v2

    :cond_1
    const/4 v1, 0x1

    if-nez v0, :cond_2

    sget-object v0, Lm/a/b1;->e:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    const/4 v2, 0x0

    invoke-virtual {v0, p0, v2, p1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return v1

    :cond_2
    instance-of v3, v0, Lm/a/p2/u;

    if-eqz v3, :cond_7

    if-eqz v0, :cond_6

    move-object v3, v0

    check-cast v3, Lm/a/p2/u;

    invoke-virtual {v3, p1}, Lm/a/p2/u;->a(Ljava/lang/Object;)I

    move-result v4

    if-eqz v4, :cond_5

    if-eq v4, v1, :cond_4

    const/4 v0, 0x2

    if-eq v4, v0, :cond_3

    goto :goto_0

    :cond_3
    return v2

    :cond_4
    sget-object v1, Lm/a/b1;->e:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v3}, Lm/a/p2/u;->i()Lm/a/p2/u;

    move-result-object v2

    invoke-virtual {v1, p0, v0, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    goto :goto_0

    :cond_5
    return v1

    :cond_6
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v0, "null cannot be cast to non-null type kotlinx.coroutines.internal.LockFreeTaskQueueCore<java.lang.Runnable{ kotlinx.coroutines.RunnableKt.Runnable }>{ kotlinx.coroutines.EventLoop_commonKt.Queue<java.lang.Runnable{ kotlinx.coroutines.RunnableKt.Runnable }> }"

    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_7
    invoke-static {}, Lm/a/e1;->a()Lm/a/p2/f0;

    move-result-object v3

    if-ne v0, v3, :cond_8

    return v2

    :cond_8
    new-instance v2, Lm/a/p2/u;

    const/16 v3, 0x8

    invoke-direct {v2, v3, v1}, Lm/a/p2/u;-><init>(IZ)V

    if-eqz v0, :cond_9

    move-object v3, v0

    check-cast v3, Ljava/lang/Runnable;

    invoke-virtual {v2, v3}, Lm/a/p2/u;->a(Ljava/lang/Object;)I

    invoke-virtual {v2, p1}, Lm/a/p2/u;->a(Ljava/lang/Object;)I

    sget-object v3, Lm/a/b1;->e:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v3, p0, v0, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return v1

    :cond_9
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v0, "null cannot be cast to non-null type java.lang.Runnable{ kotlinx.coroutines.RunnableKt.Runnable }"

    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    goto :goto_2

    :goto_1
    throw p1

    :goto_2
    goto :goto_1
.end method

.method private final n0()Z
    .locals 1

    iget v0, p0, Lm/a/b1;->_isCompleted:I

    return v0
.end method

.method private final q0()V
    .locals 4

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    move-object v0, v1

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lm/a/b;->a()J

    move-result-wide v2

    invoke-static {v2, v3}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    :goto_0
    if-nez v0, :cond_1

    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v2

    goto :goto_1

    :cond_1
    invoke-virtual {v0}, Ljava/lang/Long;->longValue()J

    move-result-wide v2

    :goto_1
    iget-object v0, p0, Lm/a/b1;->_delayed:Ljava/lang/Object;

    check-cast v0, Lm/a/b1$b;

    if-nez v0, :cond_2

    move-object v0, v1

    goto :goto_2

    :cond_2
    invoke-virtual {v0}, Lm/a/p2/k0;->i()Lm/a/p2/l0;

    move-result-object v0

    check-cast v0, Lm/a/b1$a;

    :goto_2
    if-nez v0, :cond_3

    return-void

    :cond_3
    invoke-virtual {p0, v2, v3, v0}, Lm/a/c1;->g0(JLm/a/b1$a;)V

    goto :goto_1
.end method

.method private final t0(JLm/a/b1$a;)I
    .locals 3

    invoke-direct {p0}, Lm/a/b1;->n0()Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x1

    return p1

    :cond_0
    iget-object v0, p0, Lm/a/b1;->_delayed:Ljava/lang/Object;

    check-cast v0, Lm/a/b1$b;

    if-nez v0, :cond_1

    sget-object v0, Lm/a/b1;->f:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    const/4 v1, 0x0

    new-instance v2, Lm/a/b1$b;

    invoke-direct {v2, p1, p2}, Lm/a/b1$b;-><init>(J)V

    invoke-virtual {v0, p0, v1, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    iget-object v0, p0, Lm/a/b1;->_delayed:Ljava/lang/Object;

    invoke-static {v0}, Ll/v/d/j;->b(Ljava/lang/Object;)V

    check-cast v0, Lm/a/b1$b;

    :cond_1
    invoke-virtual {p3, p1, p2, v0, p0}, Lm/a/b1$a;->h(JLm/a/b1$b;Lm/a/b1;)I

    move-result p1

    return p1
.end method

.method private final u0(Z)V
    .locals 0

    iput p1, p0, Lm/a/b1;->_isCompleted:I

    return-void
.end method

.method private final v0(Lm/a/b1$a;)Z
    .locals 1

    iget-object v0, p0, Lm/a/b1;->_delayed:Ljava/lang/Object;

    check-cast v0, Lm/a/b1$b;

    if-nez v0, :cond_0

    const/4 v0, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lm/a/p2/k0;->e()Lm/a/p2/l0;

    move-result-object v0

    check-cast v0, Lm/a/b1$a;

    :goto_0
    if-ne v0, p1, :cond_1

    const/4 p1, 0x1

    goto :goto_1

    :cond_1
    const/4 p1, 0x0

    :goto_1
    return p1
.end method


# virtual methods
.method public final S(Ll/s/g;Ljava/lang/Runnable;)V
    .locals 0

    invoke-virtual {p0, p2}, Lm/a/b1;->l0(Ljava/lang/Runnable;)V

    return-void
.end method

.method protected Y()J
    .locals 6

    invoke-super {p0}, Lm/a/a1;->Y()J

    move-result-wide v0

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-nez v4, :cond_0

    return-wide v2

    :cond_0
    iget-object v0, p0, Lm/a/b1;->_queue:Ljava/lang/Object;

    const-wide v4, 0x7fffffffffffffffL

    if-nez v0, :cond_1

    goto :goto_0

    :cond_1
    instance-of v1, v0, Lm/a/p2/u;

    if-eqz v1, :cond_7

    check-cast v0, Lm/a/p2/u;

    invoke-virtual {v0}, Lm/a/p2/u;->g()Z

    move-result v0

    if-nez v0, :cond_2

    return-wide v2

    :cond_2
    :goto_0
    iget-object v0, p0, Lm/a/b1;->_delayed:Ljava/lang/Object;

    check-cast v0, Lm/a/b1$b;

    const/4 v1, 0x0

    if-nez v0, :cond_3

    move-object v0, v1

    goto :goto_1

    :cond_3
    invoke-virtual {v0}, Lm/a/p2/k0;->e()Lm/a/p2/l0;

    move-result-object v0

    check-cast v0, Lm/a/b1$a;

    :goto_1
    if-nez v0, :cond_4

    return-wide v4

    :cond_4
    iget-wide v4, v0, Lm/a/b1$a;->a:J

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v0

    if-nez v0, :cond_5

    goto :goto_2

    :cond_5
    invoke-virtual {v0}, Lm/a/b;->a()J

    move-result-wide v0

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v1

    :goto_2
    if-nez v1, :cond_6

    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v0

    goto :goto_3

    :cond_6
    invoke-virtual {v1}, Ljava/lang/Long;->longValue()J

    move-result-wide v0

    :goto_3
    sub-long/2addr v4, v0

    invoke-static {v4, v5, v2, v3}, Ll/x/d;->b(JJ)J

    move-result-wide v0

    return-wide v0

    :cond_7
    invoke-static {}, Lm/a/e1;->a()Lm/a/p2/f0;

    move-result-object v1

    if-ne v0, v1, :cond_8

    return-wide v4

    :cond_8
    return-wide v2
.end method

.method public e0()V
    .locals 5

    sget-object v0, Lm/a/i2;->a:Lm/a/i2;

    invoke-virtual {v0}, Lm/a/i2;->b()V

    const/4 v0, 0x1

    invoke-direct {p0, v0}, Lm/a/b1;->u0(Z)V

    invoke-direct {p0}, Lm/a/b1;->j0()V

    :goto_0
    invoke-virtual {p0}, Lm/a/b1;->p0()J

    move-result-wide v0

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-gtz v4, :cond_0

    goto :goto_0

    :cond_0
    invoke-direct {p0}, Lm/a/b1;->q0()V

    return-void
.end method

.method public l0(Ljava/lang/Runnable;)V
    .locals 1

    invoke-direct {p0, p1}, Lm/a/b1;->m0(Ljava/lang/Runnable;)Z

    move-result v0

    if-eqz v0, :cond_0

    invoke-virtual {p0}, Lm/a/c1;->h0()V

    goto :goto_0

    :cond_0
    sget-object v0, Lm/a/p0;->g:Lm/a/p0;

    invoke-virtual {v0, p1}, Lm/a/p0;->l0(Ljava/lang/Runnable;)V

    :goto_0
    return-void
.end method

.method protected o0()Z
    .locals 4

    invoke-virtual {p0}, Lm/a/a1;->c0()Z

    move-result v0

    const/4 v1, 0x0

    if-nez v0, :cond_0

    return v1

    :cond_0
    iget-object v0, p0, Lm/a/b1;->_delayed:Ljava/lang/Object;

    check-cast v0, Lm/a/b1$b;

    if-eqz v0, :cond_1

    invoke-virtual {v0}, Lm/a/p2/k0;->d()Z

    move-result v0

    if-nez v0, :cond_1

    return v1

    :cond_1
    iget-object v0, p0, Lm/a/b1;->_queue:Ljava/lang/Object;

    const/4 v2, 0x1

    if-nez v0, :cond_2

    :goto_0
    const/4 v1, 0x1

    goto :goto_1

    :cond_2
    instance-of v3, v0, Lm/a/p2/u;

    if-eqz v3, :cond_3

    check-cast v0, Lm/a/p2/u;

    invoke-virtual {v0}, Lm/a/p2/u;->g()Z

    move-result v1

    goto :goto_1

    :cond_3
    invoke-static {}, Lm/a/e1;->a()Lm/a/p2/f0;

    move-result-object v3

    if-ne v0, v3, :cond_4

    goto :goto_0

    :cond_4
    :goto_1
    return v1
.end method

.method public p0()J
    .locals 9

    invoke-virtual {p0}, Lm/a/a1;->d0()Z

    move-result v0

    const-wide/16 v1, 0x0

    if-eqz v0, :cond_0

    return-wide v1

    :cond_0
    iget-object v0, p0, Lm/a/b1;->_delayed:Ljava/lang/Object;

    check-cast v0, Lm/a/b1$b;

    if-eqz v0, :cond_7

    invoke-virtual {v0}, Lm/a/p2/k0;->d()Z

    move-result v3

    if-nez v3, :cond_7

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v3

    const/4 v4, 0x0

    if-nez v3, :cond_1

    move-object v3, v4

    goto :goto_0

    :cond_1
    invoke-virtual {v3}, Lm/a/b;->a()J

    move-result-wide v5

    invoke-static {v5, v6}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v3

    :goto_0
    if-nez v3, :cond_2

    invoke-static {}, Ljava/lang/System;->nanoTime()J

    move-result-wide v5

    goto :goto_1

    :cond_2
    invoke-virtual {v3}, Ljava/lang/Long;->longValue()J

    move-result-wide v5

    :cond_3
    :goto_1
    monitor-enter v0

    :try_start_0
    invoke-virtual {v0}, Lm/a/p2/k0;->b()Lm/a/p2/l0;

    move-result-object v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    if-nez v3, :cond_4

    monitor-exit v0

    move-object v3, v4

    goto :goto_4

    :cond_4
    :try_start_1
    check-cast v3, Lm/a/b1$a;

    invoke-virtual {v3, v5, v6}, Lm/a/b1$a;->i(J)Z

    move-result v7

    const/4 v8, 0x0

    if-eqz v7, :cond_5

    invoke-direct {p0, v3}, Lm/a/b1;->m0(Ljava/lang/Runnable;)Z

    move-result v3

    goto :goto_2

    :cond_5
    const/4 v3, 0x0

    :goto_2
    if-eqz v3, :cond_6

    invoke-virtual {v0, v8}, Lm/a/p2/k0;->h(I)Lm/a/p2/l0;

    move-result-object v3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    goto :goto_3

    :cond_6
    move-object v3, v4

    :goto_3
    monitor-exit v0

    :goto_4
    check-cast v3, Lm/a/b1$a;

    if-nez v3, :cond_3

    goto :goto_5

    :catchall_0
    move-exception v1

    monitor-exit v0

    throw v1

    :cond_7
    :goto_5
    invoke-direct {p0}, Lm/a/b1;->k0()Ljava/lang/Runnable;

    move-result-object v0

    if-eqz v0, :cond_8

    invoke-interface {v0}, Ljava/lang/Runnable;->run()V

    return-wide v1

    :cond_8
    invoke-virtual {p0}, Lm/a/b1;->Y()J

    move-result-wide v0

    return-wide v0
.end method

.method protected final r0()V
    .locals 1

    const/4 v0, 0x0

    iput-object v0, p0, Lm/a/b1;->_queue:Ljava/lang/Object;

    iput-object v0, p0, Lm/a/b1;->_delayed:Ljava/lang/Object;

    return-void
.end method

.method public final s0(JLm/a/b1$a;)V
    .locals 2

    invoke-direct {p0, p1, p2, p3}, Lm/a/b1;->t0(JLm/a/b1$a;)I

    move-result v0

    if-eqz v0, :cond_2

    const/4 v1, 0x1

    if-eq v0, v1, :cond_1

    const/4 p1, 0x2

    if-ne v0, p1, :cond_0

    goto :goto_0

    :cond_0
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string p2, "unexpected result"

    invoke-virtual {p2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_1
    invoke-virtual {p0, p1, p2, p3}, Lm/a/c1;->g0(JLm/a/b1$a;)V

    goto :goto_0

    :cond_2
    invoke-direct {p0, p3}, Lm/a/b1;->v0(Lm/a/b1$a;)Z

    move-result p1

    if-eqz p1, :cond_3

    invoke-virtual {p0}, Lm/a/c1;->h0()V

    :cond_3
    :goto_0
    return-void
.end method
