.class public final Ln/e0/i/c;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final d:Lo/f;

.field public static final e:Lo/f;

.field public static final f:Lo/f;

.field public static final g:Lo/f;

.field public static final h:Lo/f;

.field public static final i:Lo/f;


# instance fields
.field public final a:Lo/f;

.field public final b:Lo/f;

.field final c:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    const-string v0, ":"

    invoke-static {v0}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v0

    sput-object v0, Ln/e0/i/c;->d:Lo/f;

    const-string v0, ":status"

    invoke-static {v0}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v0

    sput-object v0, Ln/e0/i/c;->e:Lo/f;

    const-string v0, ":method"

    invoke-static {v0}, Lo/f;->g(<PERSON><PERSON><PERSON>/lang/String;)Lo/f;

    move-result-object v0

    sput-object v0, Ln/e0/i/c;->f:Lo/f;

    const-string v0, ":path"

    invoke-static {v0}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v0

    sput-object v0, Ln/e0/i/c;->g:Lo/f;

    const-string v0, ":scheme"

    invoke-static {v0}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v0

    sput-object v0, Ln/e0/i/c;->h:Lo/f;

    const-string v0, ":authority"

    invoke-static {v0}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object v0

    sput-object v0, Ln/e0/i/c;->i:Lo/f;

    return-void
.end method

.method public constructor <init>(Ljava/lang/String;Ljava/lang/String;)V
    .locals 0

    invoke-static {p1}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object p1

    invoke-static {p2}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object p2

    invoke-direct {p0, p1, p2}, Ln/e0/i/c;-><init>(Lo/f;Lo/f;)V

    return-void
.end method

.method public constructor <init>(Lo/f;Ljava/lang/String;)V
    .locals 0

    invoke-static {p2}, Lo/f;->g(Ljava/lang/String;)Lo/f;

    move-result-object p2

    invoke-direct {p0, p1, p2}, Ln/e0/i/c;-><init>(Lo/f;Lo/f;)V

    return-void
.end method

.method public constructor <init>(Lo/f;Lo/f;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Ln/e0/i/c;->a:Lo/f;

    iput-object p2, p0, Ln/e0/i/c;->b:Lo/f;

    invoke-virtual {p1}, Lo/f;->o()I

    move-result p1

    add-int/lit8 p1, p1, 0x20

    invoke-virtual {p2}, Lo/f;->o()I

    move-result p2

    add-int/2addr p1, p2

    iput p1, p0, Ln/e0/i/c;->c:I

    return-void
.end method


# virtual methods
.method public equals(Ljava/lang/Object;)Z
    .locals 3

    instance-of v0, p1, Ln/e0/i/c;

    const/4 v1, 0x0

    if-eqz v0, :cond_0

    check-cast p1, Ln/e0/i/c;

    iget-object v0, p0, Ln/e0/i/c;->a:Lo/f;

    iget-object v2, p1, Ln/e0/i/c;->a:Lo/f;

    invoke-virtual {v0, v2}, Lo/f;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    iget-object v0, p0, Ln/e0/i/c;->b:Lo/f;

    iget-object p1, p1, Ln/e0/i/c;->b:Lo/f;

    invoke-virtual {v0, p1}, Lo/f;->equals(Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_0

    const/4 v1, 0x1

    :cond_0
    return v1
.end method

.method public hashCode()I
    .locals 2

    iget-object v0, p0, Ln/e0/i/c;->a:Lo/f;

    invoke-virtual {v0}, Lo/f;->hashCode()I

    move-result v0

    const/16 v1, 0x20f

    add-int/2addr v1, v0

    mul-int/lit8 v1, v1, 0x1f

    iget-object v0, p0, Ln/e0/i/c;->b:Lo/f;

    invoke-virtual {v0}, Lo/f;->hashCode()I

    move-result v0

    add-int/2addr v1, v0

    return v1
.end method

.method public toString()Ljava/lang/String;
    .locals 3

    const/4 v0, 0x2

    new-array v0, v0, [Ljava/lang/Object;

    iget-object v1, p0, Ln/e0/i/c;->a:Lo/f;

    invoke-virtual {v1}, Lo/f;->t()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x0

    aput-object v1, v0, v2

    iget-object v1, p0, Ln/e0/i/c;->b:Lo/f;

    invoke-virtual {v1}, Lo/f;->t()Ljava/lang/String;

    move-result-object v1

    const/4 v2, 0x1

    aput-object v1, v0, v2

    const-string v1, "%s: %s"

    invoke-static {v1, v0}, Ln/e0/c;->r(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
