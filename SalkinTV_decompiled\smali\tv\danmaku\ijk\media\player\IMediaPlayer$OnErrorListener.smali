.class public interface abstract Ltv/danmaku/ijk/media/player/IMediaPlayer$OnErrorListener;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ltv/danmaku/ijk/media/player/IMediaPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnErrorListener"
.end annotation


# virtual methods
.method public abstract onError(Ltv/danmaku/ijk/media/player/IMediaPlayer;II)Z
.end method
