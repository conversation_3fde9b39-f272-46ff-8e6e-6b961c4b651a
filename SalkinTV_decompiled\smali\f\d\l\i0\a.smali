.class public final Lf/d/l/i0/a;
.super Landroid/text/style/ClickableSpan;
.source ""


# instance fields
.field private final a:I

.field private final b:Lf/d/l/i0/b;

.field private final c:I


# direct methods
.method public constructor <init>(ILf/d/l/i0/b;I)V
    .locals 0

    invoke-direct {p0}, Landroid/text/style/ClickableSpan;-><init>()V

    iput p1, p0, Lf/d/l/i0/a;->a:I

    iput-object p2, p0, Lf/d/l/i0/a;->b:Lf/d/l/i0/b;

    iput p3, p0, Lf/d/l/i0/a;->c:I

    return-void
.end method


# virtual methods
.method public onClick(Landroid/view/View;)V
    .locals 2

    new-instance p1, Landroid/os/Bundle;

    invoke-direct {p1}, Landroid/os/Bundle;-><init>()V

    iget v0, p0, Lf/d/l/i0/a;->a:I

    const-string v1, "ACCESSIBILITY_CLICKABLE_SPAN_ID"

    invoke-virtual {p1, v1, v0}, Landroid/os/Bundle;->putInt(Ljava/lang/String;I)V

    iget-object v0, p0, Lf/d/l/i0/a;->b:Lf/d/l/i0/b;

    iget v1, p0, Lf/d/l/i0/a;->c:I

    invoke-virtual {v0, v1, p1}, Lf/d/l/i0/b;->F(ILandroid/os/Bundle;)Z

    return-void
.end method
