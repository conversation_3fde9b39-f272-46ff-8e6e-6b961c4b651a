.class final Lm/a/p2/k$b;
.super Ll/v/d/k;
.source ""

# interfaces
.implements Ll/v/c/l;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm/a/p2/k;->b(Ljava/lang/Class;)Ll/v/c/l;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation


# static fields
.field public static final a:Lm/a/p2/k$b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/p2/k$b;

    invoke-direct {v0}, Lm/a/p2/k$b;-><init>()V

    sput-object v0, Lm/a/p2/k$b;->a:Lm/a/p2/k$b;

    return-void
.end method

.method constructor <init>()V
    .locals 1

    const/4 v0, 0x1

    invoke-direct {p0, v0}, Ll/v/d/k;-><init>(I)V

    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Throwable;)Ljava/lang/Void;
    .locals 0

    const/4 p1, 0x0

    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    check-cast p1, Ljava/lang/Throwable;

    invoke-virtual {p0, p1}, Lm/a/p2/k$b;->a(Ljava/lang/Throwable;)Ljava/lang/Void;

    move-result-object p1

    return-object p1
.end method
