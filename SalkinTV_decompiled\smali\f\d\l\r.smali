.class public interface abstract Lf/d/l/r;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract onNestedFling(Landroid/view/View;FFZ)Z
.end method

.method public abstract onNestedPreFling(Landroid/view/View;FF)Z
.end method

.method public abstract onNestedPreScroll(Landroid/view/View;II[I)V
.end method

.method public abstract onNestedScroll(Landroid/view/View;IIII)V
.end method

.method public abstract onNestedScrollAccepted(Landroid/view/View;Landroid/view/View;I)V
.end method

.method public abstract onStartNestedScroll(Landroid/view/View;Landroid/view/View;I)Z
.end method

.method public abstract onStopNestedScroll(Landroid/view/View;)V
.end method
