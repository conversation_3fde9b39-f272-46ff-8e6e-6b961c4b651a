.class public abstract Lm/a/c1;
.super Lm/a/a1;
.source ""


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lm/a/a1;-><init>()V

    return-void
.end method


# virtual methods
.method protected abstract f0()Ljava/lang/Thread;
.end method

.method protected g0(JLm/a/b1$a;)V
    .locals 1

    sget-object v0, Lm/a/p0;->g:Lm/a/p0;

    invoke-virtual {v0, p1, p2, p3}, Lm/a/b1;->s0(JLm/a/b1$a;)V

    return-void
.end method

.method protected final h0()V
    .locals 2

    invoke-virtual {p0}, Lm/a/c1;->f0()Ljava/lang/Thread;

    move-result-object v0

    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v1

    if-eq v1, v0, :cond_1

    invoke-static {}, Lm/a/c;->a()Lm/a/b;

    move-result-object v1

    if-nez v1, :cond_0

    const/4 v1, 0x0

    goto :goto_0

    :cond_0
    invoke-virtual {v1, v0}, Lm/a/b;->f(Ljava/lang/Thread;)V

    sget-object v1, Ll/p;->a:Ll/p;

    :goto_0
    if-nez v1, :cond_1

    invoke-static {v0}, Ljava/util/concurrent/locks/LockSupport;->unpark(Ljava/lang/Thread;)V

    :cond_1
    return-void
.end method
