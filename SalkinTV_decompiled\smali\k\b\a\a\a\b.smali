.class public final Lk/b/a/a/a/b;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lio/flutter/embedding/engine/i/a;


# instance fields
.field private a:Lk/a/c/a/j;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method private final a(Lk/a/c/a/c;Landroid/content/Context;)V
    .locals 2

    new-instance v0, Lk/a/c/a/j;

    const-string v1, "PonnamKarthik/fluttertoast"

    invoke-direct {v0, p1, v1}, Lk/a/c/a/j;-><init>(Lk/a/c/a/c;Ljava/lang/String;)V

    iput-object v0, p0, Lk/b/a/a/a/b;->a:Lk/a/c/a/j;

    new-instance p1, Lk/b/a/a/a/c;

    invoke-direct {p1, p2}, Lk/b/a/a/a/c;-><init>(Landroid/content/Context;)V

    iget-object p2, p0, Lk/b/a/a/a/b;->a:Lk/a/c/a/j;

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p2, p1}, Lk/a/c/a/j;->e(Lk/a/c/a/j$c;)V

    :goto_0
    return-void
.end method

.method private final b()V
    .locals 2

    iget-object v0, p0, Lk/b/a/a/a/b;->a:Lk/a/c/a/j;

    const/4 v1, 0x0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v0, v1}, Lk/a/c/a/j;->e(Lk/a/c/a/j$c;)V

    :goto_0
    iput-object v1, p0, Lk/b/a/a/a/b;->a:Lk/a/c/a/j;

    return-void
.end method


# virtual methods
.method public e(Lio/flutter/embedding/engine/i/a$b;)V
    .locals 2

    const-string v0, "binding"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1}, Lio/flutter/embedding/engine/i/a$b;->b()Lk/a/c/a/c;

    move-result-object v0

    const-string v1, "binding.binaryMessenger"

    invoke-static {v0, v1}, Ll/v/d/j;->d(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-virtual {p1}, Lio/flutter/embedding/engine/i/a$b;->a()Landroid/content/Context;

    move-result-object p1

    const-string v1, "binding.applicationContext"

    invoke-static {p1, v1}, Ll/v/d/j;->d(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0, v0, p1}, Lk/b/a/a/a/b;->a(Lk/a/c/a/c;Landroid/content/Context;)V

    return-void
.end method

.method public h(Lio/flutter/embedding/engine/i/a$b;)V
    .locals 1

    const-string v0, "p0"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Lk/b/a/a/a/b;->b()V

    return-void
.end method
