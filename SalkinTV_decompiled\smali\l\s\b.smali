.class public abstract Ll/s/b;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/s/g$c;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<B::",
        "Ll/s/g$b;",
        "E::TB;>",
        "Ljava/lang/Object;",
        "Ll/s/g$c<",
        "TE;>;"
    }
.end annotation


# instance fields
.field private final a:Ll/v/c/l;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/v/c/l<",
            "Ll/s/g$b;",
            "TE;>;"
        }
    .end annotation
.end field

.field private final b:Ll/s/g$c;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/s/g$c<",
            "*>;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Ll/s/g$c;Ll/v/c/l;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g$c<",
            "TB;>;",
            "Ll/v/c/l<",
            "-",
            "Ll/s/g$b;",
            "+TE;>;)V"
        }
    .end annotation

    const-string v0, "baseKey"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "safeCast"

    invoke-static {p2, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p2, p0, Ll/s/b;->a:Ll/v/c/l;

    instance-of p2, p1, Ll/s/b;

    if-eqz p2, :cond_0

    check-cast p1, Ll/s/b;

    iget-object p1, p1, Ll/s/b;->b:Ll/s/g$c;

    :cond_0
    iput-object p1, p0, Ll/s/b;->b:Ll/s/g$c;

    return-void
.end method


# virtual methods
.method public final a(Ll/s/g$c;)Z
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g$c<",
            "*>;)Z"
        }
    .end annotation

    const-string v0, "key"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    if-eq p1, p0, :cond_1

    iget-object v0, p0, Ll/s/b;->b:Ll/s/g$c;

    if-ne v0, p1, :cond_0

    goto :goto_0

    :cond_0
    const/4 p1, 0x0

    goto :goto_1

    :cond_1
    :goto_0
    const/4 p1, 0x1

    :goto_1
    return p1
.end method

.method public final b(Ll/s/g$b;)Ll/s/g$b;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g$b;",
            ")TE;"
        }
    .end annotation

    const-string v0, "element"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p0, Ll/s/b;->a:Ll/v/c/l;

    invoke-interface {v0, p1}, Ll/v/c/l;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ll/s/g$b;

    return-object p1
.end method
