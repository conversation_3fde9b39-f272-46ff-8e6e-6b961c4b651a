.class interface abstract Ln/e0/i/h$b;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ln/e0/i/h;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x608
    name = "b"
.end annotation


# virtual methods
.method public abstract a()V
.end method

.method public abstract b(ZLn/e0/i/m;)V
.end method

.method public abstract c(ZILo/e;I)V
.end method

.method public abstract d(ZII)V
.end method

.method public abstract e(IIIZ)V
.end method

.method public abstract f(ILn/e0/i/b;)V
.end method

.method public abstract g(ZIILjava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(ZII",
            "Ljava/util/List<",
            "Ln/e0/i/c;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract h(IJ)V
.end method

.method public abstract i(IILjava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(II",
            "Ljava/util/List<",
            "Ln/e0/i/c;",
            ">;)V"
        }
    .end annotation
.end method

.method public abstract j(ILn/e0/i/b;Lo/f;)V
.end method
