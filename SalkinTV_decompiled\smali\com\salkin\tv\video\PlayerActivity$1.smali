.class Lcom/salkin/tv/video/PlayerActivity$1;
.super Landroid/os/Handler;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/salkin/tv/video/PlayerActivity;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic this$0:Lcom/salkin/tv/video/PlayerActivity;


# direct methods
.method constructor <init>(Lcom/salkin/tv/video/PlayerActivity;)V
    .locals 0

    iput-object p1, p0, Lcom/salkin/tv/video/PlayerActivity$1;->this$0:Lcom/salkin/tv/video/PlayerActivity;

    invoke-direct {p0}, Landroid/os/Handler;-><init>()V

    return-void
.end method


# virtual methods
.method public handleMessage(Landroid/os/Message;)V
    .locals 3

    invoke-super {p0, p1}, Landroid/os/Handler;->handleMessage(Landroid/os/Message;)V

    iget p1, p1, Landroid/os/Message;->what:I

    const/4 v0, 0x1

    if-ne p1, v0, :cond_2

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity$1;->this$0:Lcom/salkin/tv/video/PlayerActivity;

    iget-object p1, p1, Lcom/salkin/tv/video/PlayerActivity;->player:Lcom/salkin/tv/video/interfaces/PlayerInterface;

    invoke-interface {p1}, Lcom/salkin/tv/video/interfaces/PlayerInterface;->isPlaying()Z

    move-result p1

    if-nez p1, :cond_0

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity$1;->this$0:Lcom/salkin/tv/video/PlayerActivity;

    iget-object p1, p1, Lcom/salkin/tv/video/PlayerActivity;->mHandler:Landroid/os/Handler;

    const-wide/16 v1, 0x1388

    invoke-virtual {p1, v0, v1, v2}, Landroid/os/Handler;->sendEmptyMessageDelayed(IJ)Z

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity$1;->this$0:Lcom/salkin/tv/video/PlayerActivity;

    invoke-virtual {p1}, Lcom/salkin/tv/video/PlayerActivity;->isVip()Z

    move-result p1

    if-eqz p1, :cond_1

    iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity$1;->this$0:Lcom/salkin/tv/video/PlayerActivity;

    iget-object p1, p1, Lcom/salkin/tv/video/PlayerActivity;->playerControllerView:Lcom/salkin/tv/video/views/PlayerControllerView;

    const/16 v0, 0x8

    invoke-virtual {p1, v0}, Landroid/widget/LinearLayout;->setVisibility(I)V

    goto :goto_0

    :cond_1
    # 注释掉showVip调用，不显示VIP提示
    # iget-object p1, p0, Lcom/salkin/tv/video/PlayerActivity$1;->this$0:Lcom/salkin/tv/video/PlayerActivity;
    # invoke-virtual {p1}, Lcom/salkin/tv/video/PlayerActivity;->showVip()V

    :cond_2
    :goto_0
    return-void
.end method
