.class final Lm/a/l2;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/s/g$b;
.implements Ll/s/g$c;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Ll/s/g$b;",
        "Ll/s/g$c<",
        "Lm/a/l2;",
        ">;"
    }
.end annotation


# static fields
.field public static final a:Lm/a/l2;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    new-instance v0, Lm/a/l2;

    invoke-direct {v0}, Lm/a/l2;-><init>()V

    sput-object v0, Lm/a/l2;->a:Lm/a/l2;

    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public fold(Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            ">(TR;",
            "Ll/v/c/p<",
            "-TR;-",
            "Ll/s/g$b;",
            "+TR;>;)TR;"
        }
    .end annotation

    invoke-static {p0, p1, p2}, Ll/s/g$b$a;->a(Ll/s/g$b;Ljava/lang/Object;Ll/v/c/p;)Ljava/lang/Object;

    move-result-object p1

    return-object p1
.end method

.method public get(Ll/s/g$c;)Ll/s/g$b;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E::",
            "Ll/s/g$b;",
            ">(",
            "Ll/s/g$c<",
            "TE;>;)TE;"
        }
    .end annotation

    invoke-static {p0, p1}, Ll/s/g$b$a;->b(Ll/s/g$b;Ll/s/g$c;)Ll/s/g$b;

    move-result-object p1

    return-object p1
.end method

.method public getKey()Ll/s/g$c;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ll/s/g$c<",
            "*>;"
        }
    .end annotation

    return-object p0
.end method

.method public minusKey(Ll/s/g$c;)Ll/s/g;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g$c<",
            "*>;)",
            "Ll/s/g;"
        }
    .end annotation

    invoke-static {p0, p1}, Ll/s/g$b$a;->c(Ll/s/g$b;Ll/s/g$c;)Ll/s/g;

    move-result-object p1

    return-object p1
.end method

.method public plus(Ll/s/g;)Ll/s/g;
    .locals 0

    invoke-static {p0, p1}, Ll/s/g$b$a;->d(Ll/s/g$b;Ll/s/g;)Ll/s/g;

    move-result-object p1

    return-object p1
.end method
