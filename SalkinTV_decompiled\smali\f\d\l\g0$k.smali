.class Lf/d/l/g0$k;
.super Lf/d/l/g0$j;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf/d/l/g0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "k"
.end annotation


# static fields
.field static final q:Lf/d/l/g0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    sget-object v0, Landroid/view/WindowInsets;->CONSUMED:Landroid/view/WindowInsets;

    invoke-static {v0}, Lf/d/l/g0;->u(Landroid/view/WindowInsets;)Lf/d/l/g0;

    move-result-object v0

    sput-object v0, Lf/d/l/g0$k;->q:Lf/d/l/g0;

    return-void
.end method

.method constructor <init>(Lf/d/l/g0;Landroid/view/WindowInsets;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lf/d/l/g0$j;-><init>(Lf/d/l/g0;Landroid/view/WindowInsets;)V

    return-void
.end method

.method constructor <init>(Lf/d/l/g0;Lf/d/l/g0$k;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lf/d/l/g0$j;-><init>(Lf/d/l/g0;Lf/d/l/g0$j;)V

    return-void
.end method


# virtual methods
.method final d(Landroid/view/View;)V
    .locals 0

    return-void
.end method

.method public g(I)Lf/d/e/b;
    .locals 1

    iget-object v0, p0, Lf/d/l/g0$g;->c:Landroid/view/WindowInsets;

    invoke-static {p1}, Lf/d/l/g0$n;->a(I)I

    move-result p1

    invoke-virtual {v0, p1}, Landroid/view/WindowInsets;->getInsets(I)Landroid/graphics/Insets;

    move-result-object p1

    invoke-static {p1}, Lf/d/e/b;->d(Landroid/graphics/Insets;)Lf/d/e/b;

    move-result-object p1

    return-object p1
.end method
