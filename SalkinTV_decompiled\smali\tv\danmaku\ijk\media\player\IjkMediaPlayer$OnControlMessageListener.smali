.class public interface abstract Ltv/danmaku/ijk/media/player/IjkMediaPlayer$OnControlMessageListener;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ltv/danmaku/ijk/media/player/IjkMediaPlayer;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "OnControlMessageListener"
.end annotation


# virtual methods
.method public abstract onControlResolveSegmentUrl(I)Ljava/lang/String;
.end method
