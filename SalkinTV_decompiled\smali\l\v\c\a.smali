.class public interface abstract Ll/v/c/a;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/c;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ll/c<",
        "TR;>;"
    }
.end annotation


# virtual methods
.method public abstract invoke()Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()TR;"
        }
    .end annotation
.end method
