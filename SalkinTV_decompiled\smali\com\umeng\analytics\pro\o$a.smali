.class public Lcom/umeng/analytics/pro/o$a;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/umeng/analytics/pro/o;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# static fields
.field public static final A:I = 0x2012

.field public static final B:I = 0x2013

.field public static final C:I = 0x2014

.field public static final D:I = 0x2015

.field public static final E:I = 0x2016

.field public static final F:I = 0x2017

.field public static final a:I = 0x1001

.field public static final b:I = 0x1002

.field public static final c:I = 0x1003

.field public static final d:I = 0x1004

.field public static final e:I = 0x1005

.field public static final f:I = 0x1006

.field public static final g:I = 0x1007

.field public static final h:I = 0x1008

.field public static final i:I = 0x1009

.field public static final j:I = 0x100a

.field public static final k:I = 0x1100

.field public static final l:I = 0x1101

.field public static final m:I = 0x1102

.field public static final n:I = 0x1103

.field public static final o:I = 0x1104

.field public static final p:I = 0x1105

.field public static final q:I = 0x2001

.field public static final r:I = 0x2002

.field public static final s:I = 0x2003

.field public static final t:I = 0x2004

.field public static final u:I = 0x2005

.field public static final v:I = 0x2007

.field public static final w:I = 0x2008

.field public static final x:I = 0x2009

.field public static final y:I = 0x2010

.field public static final z:I = 0x2011


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
