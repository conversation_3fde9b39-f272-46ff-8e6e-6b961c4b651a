.class public final Lm/a/p2/i;
.super Lm/a/u0;
.source ""

# interfaces
.implements Ll/s/j/a/e;
.implements Ll/s/d;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<T:",
        "Ljava/lang/Object;",
        ">",
        "Lm/a/u0<",
        "TT;>;",
        "Ll/s/j/a/e;",
        "Ll/s/d<",
        "TT;>;"
    }
.end annotation


# static fields
.field private static final synthetic h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;


# instance fields
.field private volatile synthetic _reusableCancellableContinuation:Ljava/lang/Object;

.field public final d:Lm/a/d0;

.field public final e:Ll/s/d;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ll/s/d<",
            "TT;>;"
        }
    .end annotation
.end field

.field public f:Ljava/lang/Object;

.field public final g:Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    const-class v0, Lm/a/p2/i;

    const-class v1, Ljava/lang/Object;

    const-string v2, "_reusableCancellableContinuation"

    invoke-static {v0, v1, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->newUpdater(Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    move-result-object v0

    sput-object v0, Lm/a/p2/i;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    return-void
.end method

.method public constructor <init>(Lm/a/d0;Ll/s/d;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lm/a/d0;",
            "Ll/s/d<",
            "-TT;>;)V"
        }
    .end annotation

    const/4 v0, -0x1

    invoke-direct {p0, v0}, Lm/a/u0;-><init>(I)V

    iput-object p1, p0, Lm/a/p2/i;->d:Lm/a/d0;

    iput-object p2, p0, Lm/a/p2/i;->e:Ll/s/d;

    invoke-static {}, Lm/a/p2/j;->a()Lm/a/p2/f0;

    move-result-object p1

    iput-object p1, p0, Lm/a/p2/i;->f:Ljava/lang/Object;

    invoke-virtual {p0}, Lm/a/p2/i;->getContext()Ll/s/g;

    move-result-object p1

    invoke-static {p1}, Lm/a/p2/j0;->b(Ll/s/g;)Ljava/lang/Object;

    move-result-object p1

    iput-object p1, p0, Lm/a/p2/i;->g:Ljava/lang/Object;

    const/4 p1, 0x0

    iput-object p1, p0, Lm/a/p2/i;->_reusableCancellableContinuation:Ljava/lang/Object;

    return-void
.end method

.method private final l()Lm/a/l;
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lm/a/l<",
            "*>;"
        }
    .end annotation

    iget-object v0, p0, Lm/a/p2/i;->_reusableCancellableContinuation:Ljava/lang/Object;

    instance-of v1, v0, Lm/a/l;

    if-eqz v1, :cond_0

    check-cast v0, Lm/a/l;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return-object v0
.end method


# virtual methods
.method public a(Ljava/lang/Object;Ljava/lang/Throwable;)V
    .locals 1

    instance-of v0, p1, Lm/a/w;

    if-eqz v0, :cond_0

    check-cast p1, Lm/a/w;

    iget-object p1, p1, Lm/a/w;->b:Ll/v/c/l;

    invoke-interface {p1, p2}, Ll/v/c/l;->invoke(Ljava/lang/Object;)Ljava/lang/Object;

    :cond_0
    return-void
.end method

.method public b()Ll/s/d;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ll/s/d<",
            "TT;>;"
        }
    .end annotation

    return-object p0
.end method

.method public getCallerFrame()Ll/s/j/a/e;
    .locals 2

    iget-object v0, p0, Lm/a/p2/i;->e:Ll/s/d;

    instance-of v1, v0, Ll/s/j/a/e;

    if-eqz v1, :cond_0

    check-cast v0, Ll/s/j/a/e;

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return-object v0
.end method

.method public getContext()Ll/s/g;
    .locals 1

    iget-object v0, p0, Lm/a/p2/i;->e:Ll/s/d;

    invoke-interface {v0}, Ll/s/d;->getContext()Ll/s/g;

    move-result-object v0

    return-object v0
.end method

.method public getStackTraceElement()Ljava/lang/StackTraceElement;
    .locals 1

    const/4 v0, 0x0

    return-object v0
.end method

.method public i()Ljava/lang/Object;
    .locals 2

    iget-object v0, p0, Lm/a/p2/i;->f:Ljava/lang/Object;

    invoke-static {}, Lm/a/n0;->a()Z

    move-result v1

    if-eqz v1, :cond_2

    invoke-static {}, Lm/a/p2/j;->a()Lm/a/p2/f0;

    move-result-object v1

    if-eq v0, v1, :cond_0

    const/4 v1, 0x1

    goto :goto_0

    :cond_0
    const/4 v1, 0x0

    :goto_0
    if-eqz v1, :cond_1

    goto :goto_1

    :cond_1
    new-instance v0, Ljava/lang/AssertionError;

    invoke-direct {v0}, Ljava/lang/AssertionError;-><init>()V

    throw v0

    :cond_2
    :goto_1
    invoke-static {}, Lm/a/p2/j;->a()Lm/a/p2/f0;

    move-result-object v1

    iput-object v1, p0, Lm/a/p2/i;->f:Ljava/lang/Object;

    return-object v0
.end method

.method public final j()V
    .locals 2

    :cond_0
    iget-object v0, p0, Lm/a/p2/i;->_reusableCancellableContinuation:Ljava/lang/Object;

    sget-object v1, Lm/a/p2/j;->b:Lm/a/p2/f0;

    if-eq v0, v1, :cond_0

    return-void
.end method

.method public final k()Lm/a/l;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lm/a/l<",
            "TT;>;"
        }
    .end annotation

    :cond_0
    :goto_0
    iget-object v0, p0, Lm/a/p2/i;->_reusableCancellableContinuation:Ljava/lang/Object;

    if-nez v0, :cond_1

    sget-object v0, Lm/a/p2/j;->b:Lm/a/p2/f0;

    iput-object v0, p0, Lm/a/p2/i;->_reusableCancellableContinuation:Ljava/lang/Object;

    const/4 v0, 0x0

    return-object v0

    :cond_1
    instance-of v1, v0, Lm/a/l;

    if-eqz v1, :cond_2

    sget-object v1, Lm/a/p2/i;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    sget-object v2, Lm/a/p2/j;->b:Lm/a/p2/f0;

    invoke-virtual {v1, p0, v0, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    if-eqz v1, :cond_0

    check-cast v0, Lm/a/l;

    return-object v0

    :cond_2
    sget-object v1, Lm/a/p2/j;->b:Lm/a/p2/f0;

    if-ne v0, v1, :cond_3

    goto :goto_0

    :cond_3
    instance-of v1, v0, Ljava/lang/Throwable;

    if-eqz v1, :cond_4

    goto :goto_0

    :cond_4
    new-instance v1, Ljava/lang/IllegalStateException;

    const-string v2, "Inconsistent state "

    invoke-static {v2, v0}, Ll/v/d/j;->j(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {v1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    goto :goto_2

    :goto_1
    throw v1

    :goto_2
    goto :goto_1
.end method

.method public final m()Z
    .locals 1

    iget-object v0, p0, Lm/a/p2/i;->_reusableCancellableContinuation:Ljava/lang/Object;

    if-eqz v0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public final p(Ljava/lang/Throwable;)Z
    .locals 4

    :cond_0
    iget-object v0, p0, Lm/a/p2/i;->_reusableCancellableContinuation:Ljava/lang/Object;

    sget-object v1, Lm/a/p2/j;->b:Lm/a/p2/f0;

    invoke-static {v0, v1}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v2

    const/4 v3, 0x1

    if-eqz v2, :cond_1

    sget-object v0, Lm/a/p2/i;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v0, p0, v1, p1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return v3

    :cond_1
    instance-of v1, v0, Ljava/lang/Throwable;

    if-eqz v1, :cond_2

    return v3

    :cond_2
    sget-object v1, Lm/a/p2/i;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    const/4 v2, 0x0

    invoke-virtual {v1, p0, v0, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    const/4 p1, 0x0

    return p1
.end method

.method public final r()V
    .locals 1

    invoke-virtual {p0}, Lm/a/p2/i;->j()V

    invoke-direct {p0}, Lm/a/p2/i;->l()Lm/a/l;

    move-result-object v0

    if-nez v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {v0}, Lm/a/l;->s()V

    :goto_0
    return-void
.end method

.method public resumeWith(Ljava/lang/Object;)V
    .locals 6

    iget-object v0, p0, Lm/a/p2/i;->e:Ll/s/d;

    invoke-interface {v0}, Ll/s/d;->getContext()Ll/s/g;

    move-result-object v0

    const/4 v1, 0x0

    const/4 v2, 0x1

    invoke-static {p1, v1, v2, v1}, Lm/a/z;->d(Ljava/lang/Object;Ll/v/c/l;ILjava/lang/Object;)Ljava/lang/Object;

    move-result-object v3

    iget-object v4, p0, Lm/a/p2/i;->d:Lm/a/d0;

    invoke-virtual {v4, v0}, Lm/a/d0;->T(Ll/s/g;)Z

    move-result v4

    const/4 v5, 0x0

    if-eqz v4, :cond_0

    iput-object v3, p0, Lm/a/p2/i;->f:Ljava/lang/Object;

    iput v5, p0, Lm/a/u0;->c:I

    iget-object p1, p0, Lm/a/p2/i;->d:Lm/a/d0;

    invoke-virtual {p1, v0, p0}, Lm/a/d0;->S(Ll/s/g;Ljava/lang/Runnable;)V

    goto :goto_1

    :cond_0
    invoke-static {}, Lm/a/n0;->a()Z

    move-result v0

    sget-object v0, Lm/a/i2;->a:Lm/a/i2;

    invoke-virtual {v0}, Lm/a/i2;->a()Lm/a/a1;

    move-result-object v0

    invoke-virtual {v0}, Lm/a/a1;->b0()Z

    move-result v4

    if-eqz v4, :cond_1

    iput-object v3, p0, Lm/a/p2/i;->f:Ljava/lang/Object;

    iput v5, p0, Lm/a/u0;->c:I

    invoke-virtual {v0, p0}, Lm/a/a1;->X(Lm/a/u0;)V

    goto :goto_1

    :cond_1
    invoke-virtual {v0, v2}, Lm/a/a1;->Z(Z)V

    :try_start_0
    invoke-virtual {p0}, Lm/a/p2/i;->getContext()Ll/s/g;

    move-result-object v3

    iget-object v4, p0, Lm/a/p2/i;->g:Ljava/lang/Object;

    invoke-static {v3, v4}, Lm/a/p2/j0;->c(Ll/s/g;Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v4
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    :try_start_1
    iget-object v5, p0, Lm/a/p2/i;->e:Ll/s/d;

    invoke-interface {v5, p1}, Ll/s/d;->resumeWith(Ljava/lang/Object;)V

    sget-object p1, Ll/p;->a:Ll/p;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    :try_start_2
    invoke-static {v3, v4}, Lm/a/p2/j0;->a(Ll/s/g;Ljava/lang/Object;)V

    :cond_2
    invoke-virtual {v0}, Lm/a/a1;->d0()Z

    move-result p1

    if-nez p1, :cond_2

    goto :goto_0

    :catchall_0
    move-exception p1

    invoke-static {v3, v4}, Lm/a/p2/j0;->a(Ll/s/g;Ljava/lang/Object;)V

    throw p1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception p1

    :try_start_3
    invoke-virtual {p0, p1, v1}, Lm/a/u0;->g(Ljava/lang/Throwable;Ljava/lang/Throwable;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    :goto_0
    invoke-virtual {v0, v2}, Lm/a/a1;->V(Z)V

    :goto_1
    return-void

    :catchall_2
    move-exception p1

    invoke-virtual {v0, v2}, Lm/a/a1;->V(Z)V

    goto :goto_3

    :goto_2
    throw p1

    :goto_3
    goto :goto_2
.end method

.method public final s(Lm/a/k;)Ljava/lang/Throwable;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lm/a/k<",
            "*>;)",
            "Ljava/lang/Throwable;"
        }
    .end annotation

    :cond_0
    iget-object v0, p0, Lm/a/p2/i;->_reusableCancellableContinuation:Ljava/lang/Object;

    sget-object v1, Lm/a/p2/j;->b:Lm/a/p2/f0;

    const/4 v2, 0x0

    if-ne v0, v1, :cond_1

    sget-object v0, Lm/a/p2/i;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {v0, p0, v1, p1}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-object v2

    :cond_1
    instance-of p1, v0, Ljava/lang/Throwable;

    if-eqz p1, :cond_3

    sget-object p1, Lm/a/p2/i;->h:Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;

    invoke-virtual {p1, p0, v0, v2}, Ljava/util/concurrent/atomic/AtomicReferenceFieldUpdater;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_2

    check-cast v0, Ljava/lang/Throwable;

    return-object v0

    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "Failed requirement."

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    const-string v1, "Inconsistent state "

    invoke-static {v1, v0}, Ll/v/d/j;->j(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    goto :goto_1

    :goto_0
    throw p1

    :goto_1
    goto :goto_0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "DispatchedContinuation["

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lm/a/p2/i;->d:Lm/a/d0;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lm/a/p2/i;->e:Ll/s/d;

    invoke-static {v1}, Lm/a/o0;->c(Ll/s/d;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x5d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
