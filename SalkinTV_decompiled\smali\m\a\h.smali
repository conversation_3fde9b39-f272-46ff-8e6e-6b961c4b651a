.class final synthetic Lm/a/h;
.super Ljava/lang/Object;
.source ""


# direct methods
.method public static final a(Lm/a/j0;Ll/s/g;Lm/a/l0;Ll/v/c/p;)Lm/a/q1;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lm/a/j0;",
            "Ll/s/g;",
            "Lm/a/l0;",
            "Ll/v/c/p<",
            "-",
            "Lm/a/j0;",
            "-",
            "Ll/s/d<",
            "-",
            "Ll/p;",
            ">;+",
            "Ljava/lang/Object;",
            ">;)",
            "Lm/a/q1;"
        }
    .end annotation

    invoke-static {p0, p1}, Lm/a/c0;->d(Lm/a/j0;Ll/s/g;)Ll/s/g;

    move-result-object p0

    invoke-virtual {p2}, Lm/a/l0;->c()Z

    move-result p1

    if-eqz p1, :cond_0

    new-instance p1, Lm/a/z1;

    invoke-direct {p1, p0, p3}, Lm/a/z1;-><init>(Ll/s/g;Ll/v/c/p;)V

    goto :goto_0

    :cond_0
    new-instance p1, Lm/a/g2;

    const/4 v0, 0x1

    invoke-direct {p1, p0, v0}, Lm/a/g2;-><init>(Ll/s/g;Z)V

    :goto_0
    invoke-virtual {p1, p2, p1, p3}, Lm/a/a;->E0(Lm/a/l0;Ljava/lang/Object;Ll/v/c/p;)V

    return-object p1
.end method

.method public static synthetic b(Lm/a/j0;Ll/s/g;Lm/a/l0;Ll/v/c/p;ILjava/lang/Object;)Lm/a/q1;
    .locals 0

    and-int/lit8 p5, p4, 0x1

    if-eqz p5, :cond_0

    sget-object p1, Ll/s/h;->a:Ll/s/h;

    :cond_0
    and-int/lit8 p4, p4, 0x2

    if-eqz p4, :cond_1

    sget-object p2, Lm/a/l0;->a:Lm/a/l0;

    :cond_1
    invoke-static {p0, p1, p2, p3}, Lm/a/g;->a(Lm/a/j0;Ll/s/g;Lm/a/l0;Ll/v/c/p;)Lm/a/q1;

    move-result-object p0

    return-object p0
.end method
