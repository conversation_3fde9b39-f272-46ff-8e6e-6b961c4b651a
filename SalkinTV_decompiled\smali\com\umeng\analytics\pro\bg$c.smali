.class Lcom/umeng/analytics/pro/bg$c;
.super Lcom/umeng/analytics/pro/cg;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/umeng/analytics/pro/bg;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "c"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Lcom/umeng/analytics/pro/cg<",
        "Lcom/umeng/analytics/pro/bg;",
        ">;"
    }
.end annotation


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Lcom/umeng/analytics/pro/cg;-><init>()V

    return-void
.end method

.method synthetic constructor <init>(Lcom/umeng/analytics/pro/bg$1;)V
    .locals 0

    invoke-direct {p0}, Lcom/umeng/analytics/pro/bg$c;-><init>()V

    return-void
.end method


# virtual methods
.method public synthetic a(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/aw;)V
    .locals 0

    check-cast p2, Lcom/umeng/analytics/pro/bg;

    invoke-virtual {p0, p1, p2}, Lcom/umeng/analytics/pro/bg$c;->b(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/bg;)V

    return-void
.end method

.method public a(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/bg;)V
    .locals 1

    const/4 v0, 0x0

    iput-object v0, p2, Lcom/umeng/analytics/pro/bg;->b:Lcom/umeng/analytics/pro/bd;

    iput-object v0, p2, Lcom/umeng/analytics/pro/bg;->a:Ljava/lang/Object;

    invoke-virtual {p1}, Lcom/umeng/analytics/pro/bv;->v()S

    move-result v0

    invoke-virtual {p2, p1, v0}, Lcom/umeng/analytics/pro/bg;->a(Lcom/umeng/analytics/pro/bv;S)Ljava/lang/Object;

    move-result-object p1

    iput-object p1, p2, Lcom/umeng/analytics/pro/bg;->a:Ljava/lang/Object;

    if-eqz p1, :cond_0

    invoke-virtual {p2, v0}, Lcom/umeng/analytics/pro/bg;->a(S)Lcom/umeng/analytics/pro/bd;

    move-result-object p1

    iput-object p1, p2, Lcom/umeng/analytics/pro/bg;->b:Lcom/umeng/analytics/pro/bd;

    :cond_0
    return-void
.end method

.method public synthetic b(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/aw;)V
    .locals 0

    check-cast p2, Lcom/umeng/analytics/pro/bg;

    invoke-virtual {p0, p1, p2}, Lcom/umeng/analytics/pro/bg$c;->a(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/bg;)V

    return-void
.end method

.method public b(Lcom/umeng/analytics/pro/bv;Lcom/umeng/analytics/pro/bg;)V
    .locals 1

    invoke-virtual {p2}, Lcom/umeng/analytics/pro/bg;->a()Lcom/umeng/analytics/pro/bd;

    move-result-object v0

    if-eqz v0, :cond_0

    invoke-virtual {p2}, Lcom/umeng/analytics/pro/bg;->b()Ljava/lang/Object;

    move-result-object v0

    if-eqz v0, :cond_0

    iget-object v0, p2, Lcom/umeng/analytics/pro/bg;->b:Lcom/umeng/analytics/pro/bd;

    invoke-interface {v0}, Lcom/umeng/analytics/pro/bd;->a()S

    move-result v0

    invoke-virtual {p1, v0}, Lcom/umeng/analytics/pro/bv;->a(S)V

    invoke-virtual {p2, p1}, Lcom/umeng/analytics/pro/bg;->b(Lcom/umeng/analytics/pro/bv;)V

    return-void

    :cond_0
    new-instance p1, Lcom/umeng/analytics/pro/bw;

    const-string p2, "Cannot write a TUnion with no set value!"

    invoke-direct {p1, p2}, Lcom/umeng/analytics/pro/bw;-><init>(Ljava/lang/String;)V

    throw p1
.end method
