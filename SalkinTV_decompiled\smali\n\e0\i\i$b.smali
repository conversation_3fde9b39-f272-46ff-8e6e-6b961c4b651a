.class final Ln/e0/i/i$b;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lo/s;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ln/e0/i/i;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x12
    name = "b"
.end annotation


# instance fields
.field private final a:Lo/c;

.field private final b:Lo/c;

.field private final c:J

.field d:Z

.field e:Z

.field final synthetic f:Ln/e0/i/i;


# direct methods
.method static constructor <clinit>()V
    .locals 0

    return-void
.end method

.method constructor <init>(Ln/e0/i/i;J)V
    .locals 0

    iput-object p1, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance p1, Lo/c;

    invoke-direct {p1}, Lo/c;-><init>()V

    iput-object p1, p0, Ln/e0/i/i$b;->a:Lo/c;

    new-instance p1, Lo/c;

    invoke-direct {p1}, Lo/c;-><init>()V

    iput-object p1, p0, Ln/e0/i/i$b;->b:Lo/c;

    iput-wide p2, p0, Ln/e0/i/i$b;->c:J

    return-void
.end method

.method private b(J)V
    .locals 1

    iget-object v0, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-object v0, v0, Ln/e0/i/i;->d:Ln/e0/i/g;

    invoke-virtual {v0, p1, p2}, Ln/e0/i/g;->Z(J)V

    return-void
.end method

.method private d()V
    .locals 5

    iget-object v0, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-object v0, v0, Ln/e0/i/i;->i:Ln/e0/i/i$c;

    invoke-virtual {v0}, Lo/a;->k()V

    :goto_0
    :try_start_0
    iget-object v0, p0, Ln/e0/i/i$b;->b:Lo/c;

    invoke-virtual {v0}, Lo/c;->V()J

    move-result-wide v0

    const-wide/16 v2, 0x0

    cmp-long v4, v0, v2

    if-nez v4, :cond_0

    iget-boolean v0, p0, Ln/e0/i/i$b;->e:Z

    if-nez v0, :cond_0

    iget-boolean v0, p0, Ln/e0/i/i$b;->d:Z

    if-nez v0, :cond_0

    iget-object v0, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-object v1, v0, Ln/e0/i/i;->k:Ln/e0/i/b;

    if-nez v1, :cond_0

    invoke-virtual {v0}, Ln/e0/i/i;->r()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-object v0, v0, Ln/e0/i/i;->i:Ln/e0/i/i$c;

    invoke-virtual {v0}, Ln/e0/i/i$c;->u()V

    return-void

    :catchall_0
    move-exception v0

    iget-object v1, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-object v1, v1, Ln/e0/i/i;->i:Ln/e0/i/i$c;

    invoke-virtual {v1}, Ln/e0/i/i$c;->u()V

    goto :goto_2

    :goto_1
    throw v0

    :goto_2
    goto :goto_1
.end method


# virtual methods
.method public E(Lo/c;J)J
    .locals 10

    const-wide/16 v0, 0x0

    cmp-long v2, p2, v0

    if-ltz v2, :cond_5

    iget-object v2, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    monitor-enter v2

    :try_start_0
    invoke-direct {p0}, Ln/e0/i/i$b;->d()V

    iget-boolean v3, p0, Ln/e0/i/i$b;->d:Z

    if-nez v3, :cond_4

    iget-object v3, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-object v3, v3, Ln/e0/i/i;->k:Ln/e0/i/b;

    iget-object v4, p0, Ln/e0/i/i$b;->b:Lo/c;

    invoke-virtual {v4}, Lo/c;->V()J

    move-result-wide v4

    const-wide/16 v6, -0x1

    cmp-long v8, v4, v0

    if-lez v8, :cond_0

    iget-object v4, p0, Ln/e0/i/i$b;->b:Lo/c;

    invoke-virtual {v4}, Lo/c;->V()J

    move-result-wide v8

    invoke-static {p2, p3, v8, v9}, Ljava/lang/Math;->min(JJ)J

    move-result-wide p2

    invoke-virtual {v4, p1, p2, p3}, Lo/c;->E(Lo/c;J)J

    move-result-wide p1

    iget-object p3, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-wide v4, p3, Ln/e0/i/i;->a:J

    add-long/2addr v4, p1

    iput-wide v4, p3, Ln/e0/i/i;->a:J

    goto :goto_0

    :cond_0
    move-wide p1, v6

    :goto_0
    if-nez v3, :cond_1

    iget-object p3, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-wide v4, p3, Ln/e0/i/i;->a:J

    iget-object p3, p3, Ln/e0/i/i;->d:Ln/e0/i/g;

    iget-object p3, p3, Ln/e0/i/g;->n:Ln/e0/i/m;

    invoke-virtual {p3}, Ln/e0/i/m;->d()I

    move-result p3

    div-int/lit8 p3, p3, 0x2

    int-to-long v8, p3

    cmp-long p3, v4, v8

    if-ltz p3, :cond_1

    iget-object p3, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-object v4, p3, Ln/e0/i/i;->d:Ln/e0/i/g;

    iget v5, p3, Ln/e0/i/i;->c:I

    iget-wide v8, p3, Ln/e0/i/i;->a:J

    invoke-virtual {v4, v5, v8, v9}, Ln/e0/i/g;->e0(IJ)V

    iget-object p3, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iput-wide v0, p3, Ln/e0/i/i;->a:J

    :cond_1
    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    cmp-long p3, p1, v6

    if-eqz p3, :cond_2

    invoke-direct {p0, p1, p2}, Ln/e0/i/i$b;->b(J)V

    return-wide p1

    :cond_2
    if-nez v3, :cond_3

    return-wide v6

    :cond_3
    new-instance p1, Ln/e0/i/n;

    invoke-direct {p1, v3}, Ln/e0/i/n;-><init>(Ln/e0/i/b;)V

    throw p1

    :cond_4
    :try_start_1
    new-instance p1, Ljava/io/IOException;

    const-string p2, "stream closed"

    invoke-direct {p1, p2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw p1

    :catchall_0
    move-exception p1

    monitor-exit v2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1

    :cond_5
    new-instance p1, Ljava/lang/IllegalArgumentException;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "byteCount < 0: "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p2, p3}, Ljava/lang/StringBuilder;->append(J)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p2

    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method a(Lo/e;J)V
    .locals 11

    :goto_0
    const-wide/16 v0, 0x0

    cmp-long v2, p2, v0

    if-lez v2, :cond_6

    iget-object v2, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    monitor-enter v2

    :try_start_0
    iget-boolean v3, p0, Ln/e0/i/i$b;->e:Z

    iget-object v4, p0, Ln/e0/i/i$b;->b:Lo/c;

    invoke-virtual {v4}, Lo/c;->V()J

    move-result-wide v4

    add-long/2addr v4, p2

    iget-wide v6, p0, Ln/e0/i/i$b;->c:J

    const/4 v8, 0x1

    const/4 v9, 0x0

    cmp-long v10, v4, v6

    if-lez v10, :cond_0

    const/4 v4, 0x1

    goto :goto_1

    :cond_0
    const/4 v4, 0x0

    :goto_1
    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    if-eqz v4, :cond_1

    invoke-interface {p1, p2, p3}, Lo/e;->k(J)V

    iget-object p1, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    sget-object p2, Ln/e0/i/b;->e:Ln/e0/i/b;

    invoke-virtual {p1, p2}, Ln/e0/i/i;->f(Ln/e0/i/b;)V

    return-void

    :cond_1
    if-eqz v3, :cond_2

    invoke-interface {p1, p2, p3}, Lo/e;->k(J)V

    return-void

    :cond_2
    iget-object v2, p0, Ln/e0/i/i$b;->a:Lo/c;

    invoke-interface {p1, v2, p2, p3}, Lo/s;->E(Lo/c;J)J

    move-result-wide v2

    const-wide/16 v4, -0x1

    cmp-long v6, v2, v4

    if-eqz v6, :cond_5

    sub-long/2addr p2, v2

    iget-object v2, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    monitor-enter v2

    :try_start_1
    iget-object v3, p0, Ln/e0/i/i$b;->b:Lo/c;

    invoke-virtual {v3}, Lo/c;->V()J

    move-result-wide v3

    cmp-long v5, v3, v0

    if-nez v5, :cond_3

    goto :goto_2

    :cond_3
    const/4 v8, 0x0

    :goto_2
    iget-object v0, p0, Ln/e0/i/i$b;->b:Lo/c;

    iget-object v1, p0, Ln/e0/i/i$b;->a:Lo/c;

    invoke-virtual {v0, v1}, Lo/c;->c0(Lo/s;)J

    if-eqz v8, :cond_4

    iget-object v0, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    invoke-virtual {v0}, Ljava/lang/Object;->notifyAll()V

    :cond_4
    monitor-exit v2

    goto :goto_0

    :catchall_0
    move-exception p1

    monitor-exit v2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw p1

    :cond_5
    new-instance p1, Ljava/io/EOFException;

    invoke-direct {p1}, Ljava/io/EOFException;-><init>()V

    throw p1

    :catchall_1
    move-exception p1

    :try_start_2
    monitor-exit v2
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    throw p1

    :cond_6
    return-void
.end method

.method public close()V
    .locals 5

    iget-object v0, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    monitor-enter v0

    const/4 v1, 0x1

    :try_start_0
    iput-boolean v1, p0, Ln/e0/i/i$b;->d:Z

    iget-object v1, p0, Ln/e0/i/i$b;->b:Lo/c;

    invoke-virtual {v1}, Lo/c;->V()J

    move-result-wide v1

    iget-object v3, p0, Ln/e0/i/i$b;->b:Lo/c;

    invoke-virtual {v3}, Lo/c;->a()V

    iget-object v3, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    invoke-virtual {v3}, Ljava/lang/Object;->notifyAll()V

    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    const-wide/16 v3, 0x0

    cmp-long v0, v1, v3

    if-lez v0, :cond_0

    invoke-direct {p0, v1, v2}, Ln/e0/i/i$b;->b(J)V

    :cond_0
    iget-object v0, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    invoke-virtual {v0}, Ln/e0/i/i;->b()V

    return-void

    :catchall_0
    move-exception v1

    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    throw v1
.end method

.method public f()Lo/t;
    .locals 1

    iget-object v0, p0, Ln/e0/i/i$b;->f:Ln/e0/i/i;

    iget-object v0, v0, Ln/e0/i/i;->i:Ln/e0/i/i$c;

    return-object v0
.end method
