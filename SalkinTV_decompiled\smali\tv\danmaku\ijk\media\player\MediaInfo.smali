.class public Ltv/danmaku/ijk/media/player/MediaInfo;
.super Ljava/lang/Object;
.source ""


# instance fields
.field public mAudioDecoder:Ljava/lang/String;

.field public mAudioDecoderImpl:Ljava/lang/String;

.field public mMediaPlayerName:Ljava/lang/String;

.field public mMeta:Ltv/danmaku/ijk/media/player/IjkMediaMeta;

.field public mVideoDecoder:Ljava/lang/String;

.field public mVideoDecoderImpl:Ljava/lang/String;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
