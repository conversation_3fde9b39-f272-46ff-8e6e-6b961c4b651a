.class Lf/d/l/g0$j;
.super Lf/d/l/g0$i;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf/d/l/g0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0xa
    name = "j"
.end annotation


# instance fields
.field private n:Lf/d/e/b;

.field private o:Lf/d/e/b;

.field private p:Lf/d/e/b;


# direct methods
.method constructor <init>(Lf/d/l/g0;Landroid/view/WindowInsets;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lf/d/l/g0$i;-><init>(Lf/d/l/g0;Landroid/view/WindowInsets;)V

    const/4 p1, 0x0

    iput-object p1, p0, Lf/d/l/g0$j;->n:Lf/d/e/b;

    iput-object p1, p0, Lf/d/l/g0$j;->o:Lf/d/e/b;

    iput-object p1, p0, Lf/d/l/g0$j;->p:Lf/d/e/b;

    return-void
.end method

.method constructor <init>(Lf/d/l/g0;Lf/d/l/g0$j;)V
    .locals 0

    invoke-direct {p0, p1, p2}, Lf/d/l/g0$i;-><init>(Lf/d/l/g0;Lf/d/l/g0$i;)V

    const/4 p1, 0x0

    iput-object p1, p0, Lf/d/l/g0$j;->n:Lf/d/e/b;

    iput-object p1, p0, Lf/d/l/g0$j;->o:Lf/d/e/b;

    iput-object p1, p0, Lf/d/l/g0$j;->p:Lf/d/e/b;

    return-void
.end method


# virtual methods
.method h()Lf/d/e/b;
    .locals 1

    iget-object v0, p0, Lf/d/l/g0$j;->o:Lf/d/e/b;

    if-nez v0, :cond_0

    iget-object v0, p0, Lf/d/l/g0$g;->c:Landroid/view/WindowInsets;

    invoke-virtual {v0}, Landroid/view/WindowInsets;->getMandatorySystemGestureInsets()Landroid/graphics/Insets;

    move-result-object v0

    invoke-static {v0}, Lf/d/e/b;->d(Landroid/graphics/Insets;)Lf/d/e/b;

    move-result-object v0

    iput-object v0, p0, Lf/d/l/g0$j;->o:Lf/d/e/b;

    :cond_0
    iget-object v0, p0, Lf/d/l/g0$j;->o:Lf/d/e/b;

    return-object v0
.end method

.method j()Lf/d/e/b;
    .locals 1

    iget-object v0, p0, Lf/d/l/g0$j;->n:Lf/d/e/b;

    if-nez v0, :cond_0

    iget-object v0, p0, Lf/d/l/g0$g;->c:Landroid/view/WindowInsets;

    invoke-virtual {v0}, Landroid/view/WindowInsets;->getSystemGestureInsets()Landroid/graphics/Insets;

    move-result-object v0

    invoke-static {v0}, Lf/d/e/b;->d(Landroid/graphics/Insets;)Lf/d/e/b;

    move-result-object v0

    iput-object v0, p0, Lf/d/l/g0$j;->n:Lf/d/e/b;

    :cond_0
    iget-object v0, p0, Lf/d/l/g0$j;->n:Lf/d/e/b;

    return-object v0
.end method

.method l()Lf/d/e/b;
    .locals 1

    iget-object v0, p0, Lf/d/l/g0$j;->p:Lf/d/e/b;

    if-nez v0, :cond_0

    iget-object v0, p0, Lf/d/l/g0$g;->c:Landroid/view/WindowInsets;

    invoke-virtual {v0}, Landroid/view/WindowInsets;->getTappableElementInsets()Landroid/graphics/Insets;

    move-result-object v0

    invoke-static {v0}, Lf/d/e/b;->d(Landroid/graphics/Insets;)Lf/d/e/b;

    move-result-object v0

    iput-object v0, p0, Lf/d/l/g0$j;->p:Lf/d/e/b;

    :cond_0
    iget-object v0, p0, Lf/d/l/g0$j;->p:Lf/d/e/b;

    return-object v0
.end method

.method m(IIII)Lf/d/l/g0;
    .locals 1

    iget-object v0, p0, Lf/d/l/g0$g;->c:Landroid/view/WindowInsets;

    invoke-virtual {v0, p1, p2, p3, p4}, Landroid/view/WindowInsets;->inset(IIII)Landroid/view/WindowInsets;

    move-result-object p1

    invoke-static {p1}, Lf/d/l/g0;->u(Landroid/view/WindowInsets;)Lf/d/l/g0;

    move-result-object p1

    return-object p1
.end method

.method public s(Lf/d/e/b;)V
    .locals 0

    return-void
.end method
