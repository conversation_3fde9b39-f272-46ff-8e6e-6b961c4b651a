.class public final Lm/a/p2/c;
.super Ljava/lang/Object;
.source ""


# static fields
.field public static final a:Ljava/lang/Object;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lm/a/p2/f0;

    const-string v1, "NO_DECISION"

    invoke-direct {v0, v1}, Lm/a/p2/f0;-><init>(Ljava/lang/String;)V

    sput-object v0, Lm/a/p2/c;->a:Ljava/lang/Object;

    return-void
.end method
