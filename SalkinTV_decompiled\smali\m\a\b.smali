.class public abstract Lm/a/b;
.super Ljava/lang/Object;
.source ""


# virtual methods
.method public abstract a()J
.end method

.method public abstract b(Ljava/lang/Object;J)V
.end method

.method public abstract c()V
.end method

.method public abstract d()V
.end method

.method public abstract e()V
.end method

.method public abstract f(Ljava/lang/Thread;)V
.end method

.method public abstract g()V
.end method

.method public abstract h(Ljava/lang/Runnable;)Ljava/lang/Runnable;
.end method
