.class public interface abstract Ll/v/c/l;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ll/c;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<P1:",
        "Ljava/lang/Object;",
        "R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Ll/c<",
        "TR;>;"
    }
.end annotation


# virtual methods
.method public abstract invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TP1;)TR;"
        }
    .end annotation
.end method
