.class public abstract Lm/a/d0;
.super Ll/s/a;
.source ""

# interfaces
.implements Ll/s/e;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lm/a/d0$a;
    }
.end annotation


# static fields
.field public static final a:Lm/a/d0$a;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    new-instance v0, Lm/a/d0$a;

    const/4 v1, 0x0

    invoke-direct {v0, v1}, Lm/a/d0$a;-><init>(Ll/v/d/e;)V

    sput-object v0, Lm/a/d0;->a:Lm/a/d0$a;

    return-void
.end method

.method public constructor <init>()V
    .locals 1

    sget-object v0, Ll/s/e;->F:Ll/s/e$b;

    invoke-direct {p0, v0}, Ll/s/a;-><init>(Ll/s/g$c;)V

    return-void
.end method


# virtual methods
.method public abstract S(Ll/s/g;Ljava/lang/Runnable;)V
.end method

.method public T(Ll/s/g;)Z
    .locals 0

    const/4 p1, 0x1

    return p1
.end method

.method public U(I)Lm/a/d0;
    .locals 1

    invoke-static {p1}, Lm/a/p2/p;->a(I)V

    new-instance v0, Lm/a/p2/o;

    invoke-direct {v0, p0, p1}, Lm/a/p2/o;-><init>(Lm/a/d0;I)V

    return-object v0
.end method

.method public final d(Ll/s/d;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/d<",
            "*>;)V"
        }
    .end annotation

    check-cast p1, Lm/a/p2/i;

    invoke-virtual {p1}, Lm/a/p2/i;->r()V

    return-void
.end method

.method public get(Ll/s/g$c;)Ll/s/g$b;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<E::",
            "Ll/s/g$b;",
            ">(",
            "Ll/s/g$c<",
            "TE;>;)TE;"
        }
    .end annotation

    invoke-static {p0, p1}, Ll/s/e$a;->a(Ll/s/e;Ll/s/g$c;)Ll/s/g$b;

    move-result-object p1

    return-object p1
.end method

.method public final j(Ll/s/d;)Ll/s/d;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<T:",
            "Ljava/lang/Object;",
            ">(",
            "Ll/s/d<",
            "-TT;>;)",
            "Ll/s/d<",
            "TT;>;"
        }
    .end annotation

    new-instance v0, Lm/a/p2/i;

    invoke-direct {v0, p0, p1}, Lm/a/p2/i;-><init>(Lm/a/d0;Ll/s/d;)V

    return-object v0
.end method

.method public minusKey(Ll/s/g$c;)Ll/s/g;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ll/s/g$c<",
            "*>;)",
            "Ll/s/g;"
        }
    .end annotation

    invoke-static {p0, p1}, Ll/s/e$a;->b(Ll/s/e;Ll/s/g$c;)Ll/s/g;

    move-result-object p1

    return-object p1
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    invoke-static {p0}, Lm/a/o0;->a(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x40

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-static {p0}, Lm/a/o0;->b(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
