.class public final synthetic Lk/b/a/a/a/a;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lk/b/a/a/a/c;


# direct methods
.method public synthetic constructor <init>(Lk/b/a/a/a/c;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lk/b/a/a/a/a;->a:Lk/b/a/a/a/c;

    return-void
.end method


# virtual methods
.method public final run()V
    .locals 1

    iget-object v0, p0, Lk/b/a/a/a/a;->a:Lk/b/a/a/a/c;

    invoke-static {v0}, Lk/b/a/a/a/c;->b(Lk/b/a/a/a/c;)V

    return-void
.end method
