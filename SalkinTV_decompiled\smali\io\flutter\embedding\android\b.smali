.class public final synthetic Lio/flutter/embedding/android/b;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lio/flutter/embedding/engine/j/e$a;


# instance fields
.field public final synthetic a:Lio/flutter/embedding/android/q$d$a;


# direct methods
.method public synthetic constructor <init>(Lio/flutter/embedding/android/q$d$a;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lio/flutter/embedding/android/b;->a:Lio/flutter/embedding/android/q$d$a;

    return-void
.end method


# virtual methods
.method public final a(Z)V
    .locals 1

    iget-object v0, p0, Lio/flutter/embedding/android/b;->a:Lio/flutter/embedding/android/q$d$a;

    invoke-static {v0, p1}, Lio/flutter/embedding/android/n;->b(Lio/flutter/embedding/android/q$d$a;Z)V

    return-void
.end method
