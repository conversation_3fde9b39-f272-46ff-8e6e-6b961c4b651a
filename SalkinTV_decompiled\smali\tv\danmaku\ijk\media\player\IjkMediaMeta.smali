.class public Ltv/danmaku/ijk/media/player/IjkMediaMeta;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;
    }
.end annotation


# static fields
.field public static final AV_CH_BACK_CENTER:J = 0x100L

.field public static final AV_CH_BACK_LEFT:J = 0x10L

.field public static final AV_CH_BACK_RIGHT:J = 0x20L

.field public static final AV_CH_FRONT_CENTER:J = 0x4L

.field public static final AV_CH_FRONT_LEFT:J = 0x1L

.field public static final AV_CH_FRONT_LEFT_OF_CENTER:J = 0x40L

.field public static final AV_CH_FRONT_RIGHT:J = 0x2L

.field public static final AV_CH_FRONT_RIGHT_OF_CENTER:J = 0x80L

.field public static final AV_CH_LAYOUT_2POINT1:J = 0xbL

.field public static final AV_CH_LAYOUT_2_1:J = 0x103L

.field public static final AV_CH_LAYOUT_2_2:J = 0x603L

.field public static final AV_CH_LAYOUT_3POINT1:J = 0xfL

.field public static final AV_CH_LAYOUT_4POINT0:J = 0x107L

.field public static final AV_CH_LAYOUT_4POINT1:J = 0x10fL

.field public static final AV_CH_LAYOUT_5POINT0:J = 0x607L

.field public static final AV_CH_LAYOUT_5POINT0_BACK:J = 0x37L

.field public static final AV_CH_LAYOUT_5POINT1:J = 0x60fL

.field public static final AV_CH_LAYOUT_5POINT1_BACK:J = 0x3fL

.field public static final AV_CH_LAYOUT_6POINT0:J = 0x707L

.field public static final AV_CH_LAYOUT_6POINT0_FRONT:J = 0x6c3L

.field public static final AV_CH_LAYOUT_6POINT1:J = 0x70fL

.field public static final AV_CH_LAYOUT_6POINT1_BACK:J = 0x13fL

.field public static final AV_CH_LAYOUT_6POINT1_FRONT:J = 0x6cbL

.field public static final AV_CH_LAYOUT_7POINT0:J = 0x637L

.field public static final AV_CH_LAYOUT_7POINT0_FRONT:J = 0x6c7L

.field public static final AV_CH_LAYOUT_7POINT1:J = 0x63fL

.field public static final AV_CH_LAYOUT_7POINT1_WIDE:J = 0x6cfL

.field public static final AV_CH_LAYOUT_7POINT1_WIDE_BACK:J = 0xffL

.field public static final AV_CH_LAYOUT_HEXAGONAL:J = 0x137L

.field public static final AV_CH_LAYOUT_MONO:J = 0x4L

.field public static final AV_CH_LAYOUT_OCTAGONAL:J = 0x737L

.field public static final AV_CH_LAYOUT_QUAD:J = 0x33L

.field public static final AV_CH_LAYOUT_STEREO:J = 0x3L

.field public static final AV_CH_LAYOUT_STEREO_DOWNMIX:J = 0x60000000L

.field public static final AV_CH_LAYOUT_SURROUND:J = 0x7L

.field public static final AV_CH_LOW_FREQUENCY:J = 0x8L

.field public static final AV_CH_LOW_FREQUENCY_2:J = 0x800000000L

.field public static final AV_CH_SIDE_LEFT:J = 0x200L

.field public static final AV_CH_SIDE_RIGHT:J = 0x400L

.field public static final AV_CH_STEREO_LEFT:J = 0x20000000L

.field public static final AV_CH_STEREO_RIGHT:J = 0x40000000L

.field public static final AV_CH_SURROUND_DIRECT_LEFT:J = 0x200000000L

.field public static final AV_CH_SURROUND_DIRECT_RIGHT:J = 0x400000000L

.field public static final AV_CH_TOP_BACK_CENTER:J = 0x10000L

.field public static final AV_CH_TOP_BACK_LEFT:J = 0x8000L

.field public static final AV_CH_TOP_BACK_RIGHT:J = 0x20000L

.field public static final AV_CH_TOP_CENTER:J = 0x800L

.field public static final AV_CH_TOP_FRONT_CENTER:J = 0x2000L

.field public static final AV_CH_TOP_FRONT_LEFT:J = 0x1000L

.field public static final AV_CH_TOP_FRONT_RIGHT:J = 0x4000L

.field public static final AV_CH_WIDE_LEFT:J = 0x80000000L

.field public static final AV_CH_WIDE_RIGHT:J = 0x100000000L

.field public static final FF_PROFILE_H264_BASELINE:I = 0x42

.field public static final FF_PROFILE_H264_CAVLC_444:I = 0x2c

.field public static final FF_PROFILE_H264_CONSTRAINED:I = 0x200

.field public static final FF_PROFILE_H264_CONSTRAINED_BASELINE:I = 0x242

.field public static final FF_PROFILE_H264_EXTENDED:I = 0x58

.field public static final FF_PROFILE_H264_HIGH:I = 0x64

.field public static final FF_PROFILE_H264_HIGH_10:I = 0x6e

.field public static final FF_PROFILE_H264_HIGH_10_INTRA:I = 0x86e

.field public static final FF_PROFILE_H264_HIGH_422:I = 0x7a

.field public static final FF_PROFILE_H264_HIGH_422_INTRA:I = 0x87a

.field public static final FF_PROFILE_H264_HIGH_444:I = 0x90

.field public static final FF_PROFILE_H264_HIGH_444_INTRA:I = 0x8f4

.field public static final FF_PROFILE_H264_HIGH_444_PREDICTIVE:I = 0xf4

.field public static final FF_PROFILE_H264_INTRA:I = 0x800

.field public static final FF_PROFILE_H264_MAIN:I = 0x4d

.field public static final IJKM_KEY_AUDIO_STREAM:Ljava/lang/String; = "audio"

.field public static final IJKM_KEY_BITRATE:Ljava/lang/String; = "bitrate"

.field public static final IJKM_KEY_CHANNEL_LAYOUT:Ljava/lang/String; = "channel_layout"

.field public static final IJKM_KEY_CODEC_LEVEL:Ljava/lang/String; = "codec_level"

.field public static final IJKM_KEY_CODEC_LONG_NAME:Ljava/lang/String; = "codec_long_name"

.field public static final IJKM_KEY_CODEC_NAME:Ljava/lang/String; = "codec_name"

.field public static final IJKM_KEY_CODEC_PIXEL_FORMAT:Ljava/lang/String; = "codec_pixel_format"

.field public static final IJKM_KEY_CODEC_PROFILE:Ljava/lang/String; = "codec_profile"

.field public static final IJKM_KEY_CODEC_PROFILE_ID:Ljava/lang/String; = "codec_profile_id"

.field public static final IJKM_KEY_DURATION_US:Ljava/lang/String; = "duration_us"

.field public static final IJKM_KEY_FORMAT:Ljava/lang/String; = "format"

.field public static final IJKM_KEY_FPS_DEN:Ljava/lang/String; = "fps_den"

.field public static final IJKM_KEY_FPS_NUM:Ljava/lang/String; = "fps_num"

.field public static final IJKM_KEY_HEIGHT:Ljava/lang/String; = "height"

.field public static final IJKM_KEY_LANGUAGE:Ljava/lang/String; = "language"

.field public static final IJKM_KEY_SAMPLE_RATE:Ljava/lang/String; = "sample_rate"

.field public static final IJKM_KEY_SAR_DEN:Ljava/lang/String; = "sar_den"

.field public static final IJKM_KEY_SAR_NUM:Ljava/lang/String; = "sar_num"

.field public static final IJKM_KEY_START_US:Ljava/lang/String; = "start_us"

.field public static final IJKM_KEY_STREAMS:Ljava/lang/String; = "streams"

.field public static final IJKM_KEY_TBR_DEN:Ljava/lang/String; = "tbr_den"

.field public static final IJKM_KEY_TBR_NUM:Ljava/lang/String; = "tbr_num"

.field public static final IJKM_KEY_TIMEDTEXT_STREAM:Ljava/lang/String; = "timedtext"

.field public static final IJKM_KEY_TYPE:Ljava/lang/String; = "type"

.field public static final IJKM_KEY_VIDEO_STREAM:Ljava/lang/String; = "video"

.field public static final IJKM_KEY_WIDTH:Ljava/lang/String; = "width"

.field public static final IJKM_VAL_TYPE__AUDIO:Ljava/lang/String; = "audio"

.field public static final IJKM_VAL_TYPE__TIMEDTEXT:Ljava/lang/String; = "timedtext"

.field public static final IJKM_VAL_TYPE__UNKNOWN:Ljava/lang/String; = "unknown"

.field public static final IJKM_VAL_TYPE__VIDEO:Ljava/lang/String; = "video"


# instance fields
.field public mAudioStream:Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;

.field public mBitrate:J

.field public mDurationUS:J

.field public mFormat:Ljava/lang/String;

.field public mMediaMeta:Landroid/os/Bundle;

.field public mStartUS:J

.field public final mStreams:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;",
            ">;"
        }
    .end annotation
.end field

.field public mVideoStream:Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mStreams:Ljava/util/ArrayList;

    return-void
.end method

.method public static parse(Landroid/os/Bundle;)Ltv/danmaku/ijk/media/player/IjkMediaMeta;
    .locals 11

    if-nez p0, :cond_0

    const/4 p0, 0x0

    return-object p0

    :cond_0
    new-instance v0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;

    invoke-direct {v0}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;-><init>()V

    iput-object p0, v0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mMediaMeta:Landroid/os/Bundle;

    const-string p0, "format"

    invoke-virtual {v0, p0}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p0

    iput-object p0, v0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mFormat:Ljava/lang/String;

    const-string p0, "duration_us"

    invoke-virtual {v0, p0}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getLong(Ljava/lang/String;)J

    move-result-wide v1

    iput-wide v1, v0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mDurationUS:J

    const-string p0, "start_us"

    invoke-virtual {v0, p0}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getLong(Ljava/lang/String;)J

    move-result-wide v1

    iput-wide v1, v0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mStartUS:J

    const-string p0, "bitrate"

    invoke-virtual {v0, p0}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getLong(Ljava/lang/String;)J

    move-result-wide v1

    iput-wide v1, v0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mBitrate:J

    const-string v1, "video"

    const/4 v2, -0x1

    invoke-virtual {v0, v1, v2}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getInt(Ljava/lang/String;I)I

    move-result v3

    const-string v4, "audio"

    invoke-virtual {v0, v4, v2}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getInt(Ljava/lang/String;I)I

    move-result v5

    const-string v6, "timedtext"

    invoke-virtual {v0, v6, v2}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getInt(Ljava/lang/String;I)I

    const-string v6, "streams"

    invoke-virtual {v0, v6}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getParcelableArrayList(Ljava/lang/String;)Ljava/util/ArrayList;

    move-result-object v6

    if-nez v6, :cond_1

    return-object v0

    :cond_1
    invoke-virtual {v6}, Ljava/util/ArrayList;->iterator()Ljava/util/Iterator;

    move-result-object v6

    :goto_0
    invoke-interface {v6}, Ljava/util/Iterator;->hasNext()Z

    move-result v7

    if-eqz v7, :cond_6

    invoke-interface {v6}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v7

    check-cast v7, Landroid/os/Bundle;

    add-int/lit8 v2, v2, 0x1

    if-nez v7, :cond_2

    goto :goto_0

    :cond_2
    new-instance v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;

    invoke-direct {v8, v2}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;-><init>(I)V

    iput-object v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mMeta:Landroid/os/Bundle;

    const-string v7, "type"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    iput-object v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mType:Ljava/lang/String;

    const-string v7, "language"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    iput-object v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mLanguage:Ljava/lang/String;

    iget-object v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mType:Ljava/lang/String;

    invoke-static {v7}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v7

    if-eqz v7, :cond_3

    goto :goto_0

    :cond_3
    const-string v7, "codec_name"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    iput-object v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mCodecName:Ljava/lang/String;

    const-string v7, "codec_profile"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    iput-object v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mCodecProfile:Ljava/lang/String;

    const-string v7, "codec_long_name"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    iput-object v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mCodecLongName:Ljava/lang/String;

    invoke-virtual {v8, p0}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    int-to-long v9, v7

    iput-wide v9, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mBitrate:J

    iget-object v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mType:Ljava/lang/String;

    invoke-virtual {v7, v1}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_4

    const-string v7, "width"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    iput v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mWidth:I

    const-string v7, "height"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    iput v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mHeight:I

    const-string v7, "fps_num"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    iput v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mFpsNum:I

    const-string v7, "fps_den"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    iput v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mFpsDen:I

    const-string v7, "tbr_num"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    iput v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mTbrNum:I

    const-string v7, "tbr_den"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    iput v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mTbrDen:I

    const-string v7, "sar_num"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    iput v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mSarNum:I

    const-string v7, "sar_den"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    iput v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mSarDen:I

    if-ne v3, v2, :cond_5

    iput-object v8, v0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mVideoStream:Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;

    goto :goto_1

    :cond_4
    iget-object v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mType:Ljava/lang/String;

    invoke-virtual {v7, v4}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result v7

    if-eqz v7, :cond_5

    const-string v7, "sample_rate"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getInt(Ljava/lang/String;)I

    move-result v7

    iput v7, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mSampleRate:I

    const-string v7, "channel_layout"

    invoke-virtual {v8, v7}, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->getLong(Ljava/lang/String;)J

    move-result-wide v9

    iput-wide v9, v8, Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;->mChannelLayout:J

    if-ne v5, v2, :cond_5

    iput-object v8, v0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mAudioStream:Ltv/danmaku/ijk/media/player/IjkMediaMeta$IjkStreamMeta;

    :cond_5
    :goto_1
    iget-object v7, v0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mStreams:Ljava/util/ArrayList;

    invoke-virtual {v7, v8}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    goto/16 :goto_0

    :cond_6
    return-object v0
.end method


# virtual methods
.method public getDurationInline()Ljava/lang/String;
    .locals 8

    iget-wide v0, p0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mDurationUS:J

    const-wide/16 v2, 0x1388

    add-long/2addr v0, v2

    const-wide/32 v2, 0xf4240

    div-long/2addr v0, v2

    const-wide/16 v2, 0x3c

    div-long v4, v0, v2

    rem-long/2addr v0, v2

    div-long v6, v4, v2

    rem-long/2addr v4, v2

    sget-object v2, Ljava/util/Locale;->US:Ljava/util/Locale;

    const/4 v3, 0x3

    new-array v3, v3, [Ljava/lang/Object;

    invoke-static {v6, v7}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v6

    const/4 v7, 0x0

    aput-object v6, v3, v7

    invoke-static {v4, v5}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v4

    const/4 v5, 0x1

    aput-object v4, v3, v5

    invoke-static {v0, v1}, Ljava/lang/Long;->valueOf(J)Ljava/lang/Long;

    move-result-object v0

    const/4 v1, 0x2

    aput-object v0, v3, v1

    const-string v0, "%02d:%02d:%02d"

    invoke-static {v2, v0, v3}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method

.method public getInt(Ljava/lang/String;)I
    .locals 1

    const/4 v0, 0x0

    invoke-virtual {p0, p1, v0}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getInt(Ljava/lang/String;I)I

    move-result p1

    return p1
.end method

.method public getInt(Ljava/lang/String;I)I
    .locals 1

    invoke-virtual {p0, p1}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return p2

    :cond_0
    :try_start_0
    invoke-static {p1}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result p1
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    return p1

    :catch_0
    return p2
.end method

.method public getLong(Ljava/lang/String;)J
    .locals 2

    const-wide/16 v0, 0x0

    invoke-virtual {p0, p1, v0, v1}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getLong(Ljava/lang/String;J)J

    move-result-wide v0

    return-wide v0
.end method

.method public getLong(Ljava/lang/String;J)J
    .locals 1

    invoke-virtual {p0, p1}, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    invoke-static {p1}, Landroid/text/TextUtils;->isEmpty(Ljava/lang/CharSequence;)Z

    move-result v0

    if-eqz v0, :cond_0

    return-wide p2

    :cond_0
    :try_start_0
    invoke-static {p1}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide p1
    :try_end_0
    .catch Ljava/lang/NumberFormatException; {:try_start_0 .. :try_end_0} :catch_0

    return-wide p1

    :catch_0
    return-wide p2
.end method

.method public getParcelableArrayList(Ljava/lang/String;)Ljava/util/ArrayList;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/String;",
            ")",
            "Ljava/util/ArrayList<",
            "Landroid/os/Bundle;",
            ">;"
        }
    .end annotation

    iget-object v0, p0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mMediaMeta:Landroid/os/Bundle;

    invoke-virtual {v0, p1}, Landroid/os/Bundle;->getParcelableArrayList(Ljava/lang/String;)Ljava/util/ArrayList;

    move-result-object p1

    return-object p1
.end method

.method public getString(Ljava/lang/String;)Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Ltv/danmaku/ijk/media/player/IjkMediaMeta;->mMediaMeta:Landroid/os/Bundle;

    invoke-virtual {v0, p1}, Landroid/os/Bundle;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method
