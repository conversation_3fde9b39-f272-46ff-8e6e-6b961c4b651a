.class public final Lk/b/a/a/a/c;
.super Ljava/lang/Object;
.source ""

# interfaces
.implements Lk/a/c/a/j$c;


# instance fields
.field private a:Landroid/content/Context;

.field private b:Landroid/widget/Toast;


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 1

    const-string v0, "context"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lk/b/a/a/a/c;->a:Landroid/content/Context;

    return-void
.end method

.method public static final synthetic a(Lk/b/a/a/a/c;Landroid/widget/Toast;)V
    .locals 0

    iput-object p1, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    return-void
.end method

.method public static synthetic b(Lk/b/a/a/a/c;)V
    .locals 0

    invoke-static {p0}, Lk/b/a/a/a/c;->c(Lk/b/a/a/a/c;)V

    return-void
.end method

.method private static final c(Lk/b/a/a/a/c;)V
    .locals 1

    const-string v0, "this$0"

    invoke-static {p0, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object p0, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    if-nez p0, :cond_0

    goto :goto_0

    :cond_0
    invoke-virtual {p0}, Landroid/widget/Toast;->show()V

    :goto_0
    return-void
.end method


# virtual methods
.method public onMethodCall(Lk/a/c/a/i;Lk/a/c/a/j$d;)V
    .locals 12

    const-string v0, "call"

    invoke-static {p1, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    const-string v0, "result"

    invoke-static {p2, v0}, Ll/v/d/j;->e(Ljava/lang/Object;Ljava/lang/String;)V

    iget-object v0, p1, Lk/a/c/a/i;->a:Ljava/lang/String;

    const-string v1, "showToast"

    invoke-static {v0, v1}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_13

    const-string v0, "msg"

    invoke-virtual {p1, v0}, Lk/a/c/a/i;->a(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    invoke-static {v0}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v0

    const-string v1, "length"

    invoke-virtual {p1, v1}, Lk/a/c/a/i;->a(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v1

    invoke-static {v1}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    const-string v3, "gravity"

    invoke-virtual {p1, v3}, Lk/a/c/a/i;->a(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v3

    invoke-static {v3}, Ljava/lang/String;->valueOf(Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v3

    const-string v4, "bgcolor"

    invoke-virtual {p1, v4}, Lk/a/c/a/i;->a(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Ljava/lang/Number;

    const-string v5, "textcolor"

    invoke-virtual {p1, v5}, Lk/a/c/a/i;->a(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v5

    check-cast v5, Ljava/lang/Number;

    const-string v6, "fontSize"

    invoke-virtual {p1, v6}, Lk/a/c/a/i;->a(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Ljava/lang/Number;

    const-string v6, "top"

    invoke-static {v3, v6}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v6

    const/16 v7, 0x11

    const/16 v8, 0x30

    if-eqz v6, :cond_0

    const/16 v3, 0x30

    goto :goto_0

    :cond_0
    const-string v6, "center"

    invoke-static {v3, v6}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v3

    if-eqz v3, :cond_1

    const/16 v3, 0x11

    goto :goto_0

    :cond_1
    const/16 v3, 0x50

    :goto_0
    const-string v6, "long"

    invoke-static {v1, v6}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result v1

    const/16 v6, 0x1f

    if-eqz v4, :cond_7

    sget v9, Landroid/os/Build$VERSION;->SDK_INT:I

    if-gt v9, v6, :cond_7

    iget-object v10, p0, Lk/b/a/a/a/c;->a:Landroid/content/Context;

    const-string v11, "layout_inflater"

    invoke-virtual {v10, v11}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v10

    if-eqz v10, :cond_6

    check-cast v10, Landroid/view/LayoutInflater;

    sget v11, Lk/b/a/a/a/f;->a:I

    invoke-virtual {v10, v11, v2}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;)Landroid/view/View;

    move-result-object v2

    sget v10, Lk/b/a/a/a/e;->a:I

    invoke-virtual {v2, v10}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v10

    check-cast v10, Landroid/widget/TextView;

    invoke-virtual {v10, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    const/16 v0, 0x15

    if-lt v9, v0, :cond_2

    iget-object v0, p0, Lk/b/a/a/a/c;->a:Landroid/content/Context;

    sget v9, Lk/b/a/a/a/d;->a:I

    invoke-virtual {v0, v9}, Landroid/content/Context;->getDrawable(I)Landroid/graphics/drawable/Drawable;

    move-result-object v0

    invoke-static {v0}, Ll/v/d/j;->b(Ljava/lang/Object;)V

    goto :goto_1

    :cond_2
    iget-object v0, p0, Lk/b/a/a/a/c;->a:Landroid/content/Context;

    sget v9, Lk/b/a/a/a/d;->a:I

    invoke-static {v0, v9}, Lf/d/d/a;->d(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    move-result-object v0

    :goto_1
    invoke-static {v0}, Ll/v/d/j;->b(Ljava/lang/Object;)V

    invoke-virtual {v4}, Ljava/lang/Number;->intValue()I

    move-result v4

    sget-object v9, Landroid/graphics/PorterDuff$Mode;->SRC_IN:Landroid/graphics/PorterDuff$Mode;

    invoke-virtual {v0, v4, v9}, Landroid/graphics/drawable/Drawable;->setColorFilter(ILandroid/graphics/PorterDuff$Mode;)V

    invoke-virtual {v10, v0}, Landroid/widget/TextView;->setBackground(Landroid/graphics/drawable/Drawable;)V

    if-eqz p1, :cond_3

    invoke-virtual {p1}, Ljava/lang/Number;->floatValue()F

    move-result p1

    invoke-virtual {v10, p1}, Landroid/widget/TextView;->setTextSize(F)V

    :cond_3
    if-eqz v5, :cond_4

    invoke-virtual {v5}, Ljava/lang/Number;->intValue()I

    move-result p1

    invoke-virtual {v10, p1}, Landroid/widget/TextView;->setTextColor(I)V

    :cond_4
    new-instance p1, Landroid/widget/Toast;

    iget-object v0, p0, Lk/b/a/a/a/c;->a:Landroid/content/Context;

    invoke-direct {p1, v0}, Landroid/widget/Toast;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    invoke-virtual {p1, v1}, Landroid/widget/Toast;->setDuration(I)V

    iget-object p1, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    if-nez p1, :cond_5

    goto :goto_3

    :cond_5
    invoke-virtual {p1, v2}, Landroid/widget/Toast;->setView(Landroid/view/View;)V

    goto :goto_3

    :cond_6
    new-instance p1, Ljava/lang/NullPointerException;

    const-string p2, "null cannot be cast to non-null type android.view.LayoutInflater"

    invoke-direct {p1, p2}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1

    :cond_7
    iget-object v4, p0, Lk/b/a/a/a/c;->a:Landroid/content/Context;

    invoke-static {v4, v0, v1}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;

    move-result-object v0

    iput-object v0, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    sget v1, Landroid/os/Build$VERSION;->SDK_INT:I

    if-gt v1, v6, :cond_a

    if-nez v0, :cond_8

    goto :goto_2

    :cond_8
    :try_start_0
    invoke-virtual {v0}, Landroid/widget/Toast;->getView()Landroid/view/View;

    move-result-object v2

    :goto_2
    invoke-static {v2}, Ll/v/d/j;->b(Ljava/lang/Object;)V

    const v0, 0x102000b

    invoke-virtual {v2, v0}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    move-result-object v0

    const-string v1, "mToast?.view!!.findViewById(android.R.id.message)"

    invoke-static {v0, v1}, Ll/v/d/j;->d(Ljava/lang/Object;Ljava/lang/String;)V

    check-cast v0, Landroid/widget/TextView;

    if-eqz p1, :cond_9

    invoke-virtual {p1}, Ljava/lang/Number;->floatValue()F

    move-result p1

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setTextSize(F)V

    :cond_9
    if-eqz v5, :cond_a

    invoke-virtual {v5}, Ljava/lang/Number;->intValue()I

    move-result p1

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setTextColor(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_3

    :catch_0
    nop

    :cond_a
    :goto_3
    sget p1, Landroid/os/Build$VERSION;->SDK_INT:I

    if-gt p1, v6, :cond_f

    const/4 v0, 0x0

    if-eq v3, v7, :cond_d

    const/16 v1, 0x64

    iget-object v2, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    if-eq v3, v8, :cond_b

    if-nez v2, :cond_c

    goto :goto_4

    :cond_b
    if-nez v2, :cond_c

    goto :goto_4

    :cond_c
    invoke-virtual {v2, v3, v0, v1}, Landroid/widget/Toast;->setGravity(III)V

    goto :goto_4

    :cond_d
    iget-object v1, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    if-nez v1, :cond_e

    goto :goto_4

    :cond_e
    invoke-virtual {v1, v3, v0, v0}, Landroid/widget/Toast;->setGravity(III)V

    :cond_f
    :goto_4
    iget-object v0, p0, Lk/b/a/a/a/c;->a:Landroid/content/Context;

    instance-of v1, v0, Landroid/app/Activity;

    if-eqz v1, :cond_10

    check-cast v0, Landroid/app/Activity;

    new-instance v1, Lk/b/a/a/a/a;

    invoke-direct {v1, p0}, Lk/b/a/a/a/a;-><init>(Lk/b/a/a/a/c;)V

    invoke-virtual {v0, v1}, Landroid/app/Activity;->runOnUiThread(Ljava/lang/Runnable;)V

    goto :goto_5

    :cond_10
    iget-object v0, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    if-nez v0, :cond_11

    goto :goto_5

    :cond_11
    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    :goto_5
    const/16 v0, 0x1e

    if-lt p1, v0, :cond_15

    iget-object p1, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    if-nez p1, :cond_12

    goto :goto_7

    :cond_12
    new-instance v0, Lk/b/a/a/a/c$a;

    invoke-direct {v0, p0}, Lk/b/a/a/a/c$a;-><init>(Lk/b/a/a/a/c;)V

    invoke-virtual {p1, v0}, Landroid/widget/Toast;->addCallback(Landroid/widget/Toast$Callback;)V

    goto :goto_7

    :cond_13
    const-string p1, "cancel"

    invoke-static {v0, p1}, Ll/v/d/j;->a(Ljava/lang/Object;Ljava/lang/Object;)Z

    move-result p1

    if-eqz p1, :cond_16

    iget-object p1, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    if-eqz p1, :cond_15

    if-nez p1, :cond_14

    goto :goto_6

    :cond_14
    invoke-virtual {p1}, Landroid/widget/Toast;->cancel()V

    :goto_6
    iput-object v2, p0, Lk/b/a/a/a/c;->b:Landroid/widget/Toast;

    :cond_15
    :goto_7
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    invoke-interface {p2, p1}, Lk/a/c/a/j$d;->success(Ljava/lang/Object;)V

    goto :goto_8

    :cond_16
    invoke-interface {p2}, Lk/a/c/a/j$d;->notImplemented()V

    :goto_8
    return-void
.end method
