.class public final Ln/v$b;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ln/v;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field A:I

.field a:Ln/n;

.field b:Ljava/net/Proxy;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field c:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/w;",
            ">;"
        }
    .end annotation
.end field

.field d:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/k;",
            ">;"
        }
    .end annotation
.end field

.field final e:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/t;",
            ">;"
        }
    .end annotation
.end field

.field final f:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ln/t;",
            ">;"
        }
    .end annotation
.end field

.field g:Ln/p$c;

.field h:Ljava/net/ProxySelector;

.field i:Ln/m;

.field j:Ln/c;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field k:Ln/e0/e/d;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field l:Ljavax/net/SocketFactory;

.field m:Ljavax/net/ssl/SSLSocketFactory;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field n:Ln/e0/k/c;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field o:Ljavax/net/ssl/HostnameVerifier;

.field p:Ln/g;

.field q:Ln/b;

.field r:Ln/b;

.field s:Ln/j;

.field t:Ln/o;

.field u:Z

.field v:Z

.field w:Z

.field x:I

.field y:I

.field z:I


# direct methods
.method public constructor <init>()V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Ln/v$b;->e:Ljava/util/List;

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Ln/v$b;->f:Ljava/util/List;

    new-instance v0, Ln/n;

    invoke-direct {v0}, Ln/n;-><init>()V

    iput-object v0, p0, Ln/v$b;->a:Ln/n;

    sget-object v0, Ln/v;->B:Ljava/util/List;

    iput-object v0, p0, Ln/v$b;->c:Ljava/util/List;

    sget-object v0, Ln/v;->C:Ljava/util/List;

    iput-object v0, p0, Ln/v$b;->d:Ljava/util/List;

    sget-object v0, Ln/p;->NONE:Ln/p;

    invoke-static {v0}, Ln/p;->factory(Ln/p;)Ln/p$c;

    move-result-object v0

    iput-object v0, p0, Ln/v$b;->g:Ln/p$c;

    invoke-static {}, Ljava/net/ProxySelector;->getDefault()Ljava/net/ProxySelector;

    move-result-object v0

    iput-object v0, p0, Ln/v$b;->h:Ljava/net/ProxySelector;

    sget-object v0, Ln/m;->a:Ln/m;

    iput-object v0, p0, Ln/v$b;->i:Ln/m;

    invoke-static {}, Ljavax/net/SocketFactory;->getDefault()Ljavax/net/SocketFactory;

    move-result-object v0

    iput-object v0, p0, Ln/v$b;->l:Ljavax/net/SocketFactory;

    sget-object v0, Ln/e0/k/d;->a:Ln/e0/k/d;

    iput-object v0, p0, Ln/v$b;->o:Ljavax/net/ssl/HostnameVerifier;

    sget-object v0, Ln/g;->c:Ln/g;

    iput-object v0, p0, Ln/v$b;->p:Ln/g;

    sget-object v0, Ln/b;->a:Ln/b;

    iput-object v0, p0, Ln/v$b;->q:Ln/b;

    iput-object v0, p0, Ln/v$b;->r:Ln/b;

    new-instance v0, Ln/j;

    invoke-direct {v0}, Ln/j;-><init>()V

    iput-object v0, p0, Ln/v$b;->s:Ln/j;

    sget-object v0, Ln/o;->a:Ln/o;

    iput-object v0, p0, Ln/v$b;->t:Ln/o;

    const/4 v0, 0x1

    iput-boolean v0, p0, Ln/v$b;->u:Z

    iput-boolean v0, p0, Ln/v$b;->v:Z

    iput-boolean v0, p0, Ln/v$b;->w:Z

    const/16 v0, 0x2710

    iput v0, p0, Ln/v$b;->x:I

    iput v0, p0, Ln/v$b;->y:I

    iput v0, p0, Ln/v$b;->z:I

    const/4 v0, 0x0

    iput v0, p0, Ln/v$b;->A:I

    return-void
.end method

.method constructor <init>(Ln/v;)V
    .locals 3

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    iput-object v0, p0, Ln/v$b;->e:Ljava/util/List;

    new-instance v1, Ljava/util/ArrayList;

    invoke-direct {v1}, Ljava/util/ArrayList;-><init>()V

    iput-object v1, p0, Ln/v$b;->f:Ljava/util/List;

    iget-object v2, p1, Ln/v;->a:Ln/n;

    iput-object v2, p0, Ln/v$b;->a:Ln/n;

    iget-object v2, p1, Ln/v;->b:Ljava/net/Proxy;

    iput-object v2, p0, Ln/v$b;->b:Ljava/net/Proxy;

    iget-object v2, p1, Ln/v;->c:Ljava/util/List;

    iput-object v2, p0, Ln/v$b;->c:Ljava/util/List;

    iget-object v2, p1, Ln/v;->d:Ljava/util/List;

    iput-object v2, p0, Ln/v$b;->d:Ljava/util/List;

    iget-object v2, p1, Ln/v;->e:Ljava/util/List;

    invoke-interface {v0, v2}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    iget-object v0, p1, Ln/v;->f:Ljava/util/List;

    invoke-interface {v1, v0}, Ljava/util/List;->addAll(Ljava/util/Collection;)Z

    iget-object v0, p1, Ln/v;->g:Ln/p$c;

    iput-object v0, p0, Ln/v$b;->g:Ln/p$c;

    iget-object v0, p1, Ln/v;->h:Ljava/net/ProxySelector;

    iput-object v0, p0, Ln/v$b;->h:Ljava/net/ProxySelector;

    iget-object v0, p1, Ln/v;->i:Ln/m;

    iput-object v0, p0, Ln/v$b;->i:Ln/m;

    iget-object v0, p1, Ln/v;->k:Ln/e0/e/d;

    iput-object v0, p0, Ln/v$b;->k:Ln/e0/e/d;

    iget-object v0, p1, Ln/v;->j:Ln/c;

    iget-object v0, p1, Ln/v;->l:Ljavax/net/SocketFactory;

    iput-object v0, p0, Ln/v$b;->l:Ljavax/net/SocketFactory;

    iget-object v0, p1, Ln/v;->m:Ljavax/net/ssl/SSLSocketFactory;

    iput-object v0, p0, Ln/v$b;->m:Ljavax/net/ssl/SSLSocketFactory;

    iget-object v0, p1, Ln/v;->n:Ln/e0/k/c;

    iput-object v0, p0, Ln/v$b;->n:Ln/e0/k/c;

    iget-object v0, p1, Ln/v;->o:Ljavax/net/ssl/HostnameVerifier;

    iput-object v0, p0, Ln/v$b;->o:Ljavax/net/ssl/HostnameVerifier;

    iget-object v0, p1, Ln/v;->p:Ln/g;

    iput-object v0, p0, Ln/v$b;->p:Ln/g;

    iget-object v0, p1, Ln/v;->q:Ln/b;

    iput-object v0, p0, Ln/v$b;->q:Ln/b;

    iget-object v0, p1, Ln/v;->r:Ln/b;

    iput-object v0, p0, Ln/v$b;->r:Ln/b;

    iget-object v0, p1, Ln/v;->s:Ln/j;

    iput-object v0, p0, Ln/v$b;->s:Ln/j;

    iget-object v0, p1, Ln/v;->t:Ln/o;

    iput-object v0, p0, Ln/v$b;->t:Ln/o;

    iget-boolean v0, p1, Ln/v;->u:Z

    iput-boolean v0, p0, Ln/v$b;->u:Z

    iget-boolean v0, p1, Ln/v;->v:Z

    iput-boolean v0, p0, Ln/v$b;->v:Z

    iget-boolean v0, p1, Ln/v;->w:Z

    iput-boolean v0, p0, Ln/v$b;->w:Z

    iget v0, p1, Ln/v;->x:I

    iput v0, p0, Ln/v$b;->x:I

    iget v0, p1, Ln/v;->y:I

    iput v0, p0, Ln/v$b;->y:I

    iget v0, p1, Ln/v;->z:I

    iput v0, p0, Ln/v$b;->z:I

    iget p1, p1, Ln/v;->A:I

    iput p1, p0, Ln/v$b;->A:I

    return-void
.end method


# virtual methods
.method public a(Ln/t;)Ln/v$b;
    .locals 1

    if-eqz p1, :cond_0

    iget-object v0, p0, Ln/v$b;->f:Ljava/util/List;

    invoke-interface {v0, p1}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    const-string v0, "interceptor == null"

    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public b()Ln/v;
    .locals 1

    new-instance v0, Ln/v;

    invoke-direct {v0, p0}, Ln/v;-><init>(Ln/v$b;)V

    return-object v0
.end method

.method public c(JLjava/util/concurrent/TimeUnit;)Ln/v$b;
    .locals 1

    const-string v0, "timeout"

    invoke-static {v0, p1, p2, p3}, Ln/e0/c;->e(Ljava/lang/String;JLjava/util/concurrent/TimeUnit;)I

    move-result p1

    iput p1, p0, Ln/v$b;->x:I

    return-object p0
.end method

.method public d(Ljava/util/List;)Ln/v$b;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Ln/k;",
            ">;)",
            "Ln/v$b;"
        }
    .end annotation

    invoke-static {p1}, Ln/e0/c;->t(Ljava/util/List;)Ljava/util/List;

    move-result-object p1

    iput-object p1, p0, Ln/v$b;->d:Ljava/util/List;

    return-object p0
.end method

.method public e(Ln/p$c;)Ln/v$b;
    .locals 1

    if-eqz p1, :cond_0

    iput-object p1, p0, Ln/v$b;->g:Ln/p$c;

    return-object p0

    :cond_0
    new-instance p1, Ljava/lang/NullPointerException;

    const-string v0, "eventListenerFactory == null"

    invoke-direct {p1, v0}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    throw p1
.end method

.method public f(JLjava/util/concurrent/TimeUnit;)Ln/v$b;
    .locals 1

    const-string v0, "timeout"

    invoke-static {v0, p1, p2, p3}, Ln/e0/c;->e(Ljava/lang/String;JLjava/util/concurrent/TimeUnit;)I

    move-result p1

    iput p1, p0, Ln/v$b;->y:I

    return-object p0
.end method

.method public g(JLjava/util/concurrent/TimeUnit;)Ln/v$b;
    .locals 1

    const-string v0, "timeout"

    invoke-static {v0, p1, p2, p3}, Ln/e0/c;->e(Ljava/lang/String;JLjava/util/concurrent/TimeUnit;)I

    move-result p1

    iput p1, p0, Ln/v$b;->z:I

    return-object p0
.end method
