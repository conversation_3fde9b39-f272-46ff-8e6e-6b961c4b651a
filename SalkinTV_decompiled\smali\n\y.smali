.class public final Ln/y;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ln/y$a;
    }
.end annotation


# instance fields
.field final a:Ln/s;

.field final b:Ljava/lang/String;

.field final c:Ln/r;

.field final d:Ln/z;
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation
.end field

.field final e:Ljava/util/Map;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/Map<",
            "Ljava/lang/Class<",
            "*>;",
            "Ljava/lang/Object;",
            ">;"
        }
    .end annotation
.end field

.field private volatile f:Ln/d;


# direct methods
.method constructor <init>(Ln/y$a;)V
    .locals 1

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iget-object v0, p1, Ln/y$a;->a:Ln/s;

    iput-object v0, p0, Ln/y;->a:Ln/s;

    iget-object v0, p1, Ln/y$a;->b:Ljava/lang/String;

    iput-object v0, p0, Ln/y;->b:Ljava/lang/String;

    iget-object v0, p1, Ln/y$a;->c:Ln/r$a;

    invoke-virtual {v0}, Ln/r$a;->d()Ln/r;

    move-result-object v0

    iput-object v0, p0, Ln/y;->c:Ln/r;

    iget-object v0, p1, Ln/y$a;->d:Ln/z;

    iput-object v0, p0, Ln/y;->d:Ln/z;

    iget-object p1, p1, Ln/y$a;->e:Ljava/util/Map;

    invoke-static {p1}, Ln/e0/c;->v(Ljava/util/Map;)Ljava/util/Map;

    move-result-object p1

    iput-object p1, p0, Ln/y;->e:Ljava/util/Map;

    return-void
.end method


# virtual methods
.method public a()Ln/z;
    .locals 1
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Ln/y;->d:Ln/z;

    return-object v0
.end method

.method public b()Ln/d;
    .locals 1

    iget-object v0, p0, Ln/y;->f:Ln/d;

    if-eqz v0, :cond_0

    goto :goto_0

    :cond_0
    iget-object v0, p0, Ln/y;->c:Ln/r;

    invoke-static {v0}, Ln/d;->l(Ln/r;)Ln/d;

    move-result-object v0

    iput-object v0, p0, Ln/y;->f:Ln/d;

    :goto_0
    return-object v0
.end method

.method public c(Ljava/lang/String;)Ljava/lang/String;
    .locals 1
    .annotation runtime Ljavax/annotation/Nullable;
    .end annotation

    iget-object v0, p0, Ln/y;->c:Ln/r;

    invoke-virtual {v0, p1}, Ln/r;->a(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    return-object p1
.end method

.method public d()Ln/r;
    .locals 1

    iget-object v0, p0, Ln/y;->c:Ln/r;

    return-object v0
.end method

.method public e()Z
    .locals 1

    iget-object v0, p0, Ln/y;->a:Ln/s;

    invoke-virtual {v0}, Ln/s;->m()Z

    move-result v0

    return v0
.end method

.method public f()Ljava/lang/String;
    .locals 1

    iget-object v0, p0, Ln/y;->b:Ljava/lang/String;

    return-object v0
.end method

.method public g()Ln/y$a;
    .locals 1

    new-instance v0, Ln/y$a;

    invoke-direct {v0, p0}, Ln/y$a;-><init>(Ln/y;)V

    return-object v0
.end method

.method public h()Ln/s;
    .locals 1

    iget-object v0, p0, Ln/y;->a:Ln/s;

    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "Request{method="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Ln/y;->b:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, ", url="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Ln/y;->a:Ln/s;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const-string v1, ", tags="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Ln/y;->e:Ljava/util/Map;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    const/16 v1, 0x7d

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    return-object v0
.end method
